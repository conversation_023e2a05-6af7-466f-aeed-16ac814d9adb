#include "async_login_manager.h"
#include "../legacy/api/orderapi.h"
#include <QtConcurrent>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDebug>

AsyncLoginManager::AsyncLoginManager(QObject *parent)
    : QObject(parent)
    , m_threadPool(QThreadPool::globalInstance())
    , m_totalTasks(0)
    , m_completedTasks(0)
    , m_successfulTasks(0)
    , m_stopped(false)
{
    // 设置线程池最大线程数（避免过多并发）
    m_threadPool->setMaxThreadCount(qMin(9, QThread::idealThreadCount()));
}

AsyncLoginManager::~AsyncLoginManager()
{
    stopAllTasks();
}

void AsyncLoginManager::startBatchLogin(const QList<LoginTask>& tasks)
{
    if (tasks.isEmpty()) {
        emit batchLoginCompleted(0, 0);
        return;
    }

    // 重置状态
    m_stopped = false;
    m_totalTasks = tasks.size();
    m_completedTasks = 0;
    m_successfulTasks = 0;

    // 为每个任务启动异步执行
    for (int i = 0; i < tasks.size(); ++i) {
        const LoginTask& task = tasks[i];
        
        // 使用QtConcurrent在线程池中异步执行
        QtConcurrent::run(m_threadPool, [this, task]() {
            if (!m_stopped) {
                executeLoginTask(task);
            }
        });
        
        // 错开启动时间，避免同时发起太多连接
        if (i < tasks.size() - 1) {
            QThread::msleep(100); // 延迟100ms
        }
    }
}

void AsyncLoginManager::stopAllTasks()
{
    m_stopped = true;
    // 等待所有任务完成（最多等待5秒）
    m_threadPool->waitForDone(5000);
}

void AsyncLoginManager::executeLoginTask(const LoginTask& task)
{
    if (m_stopped || !task.api) {
        return;
    }

    try {
        // 在工作线程中执行登录（这里会阻塞，但不影响主线程）
        task.api->login(task.username, task.password, task.proxyString, 
                       [this, task](const QString& response) {
            // 处理登录响应
            processLoginResponse(task.accountIndex, response, task.callback);
        });
    } catch (const std::exception& e) {
        // 处理异常
        LoginResult result;
        result.accountIndex = task.accountIndex;
        result.success = false;
        result.message = QString("登录异常: %1").arg(e.what());
        
        QMetaObject::invokeMethod(this, [this, result]() {
            onTaskCompleted(result);
        }, Qt::QueuedConnection);
    }
}

void AsyncLoginManager::processLoginResponse(int accountIndex, const QString& response, 
                                           const std::function<void(int, bool, const QString&)>& callback)
{
    LoginResult result;
    result.accountIndex = accountIndex;
    
    // 解析登录响应
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        result.success = false;
        result.message = "JSON解析失败: " + parseError.errorString();
    } else if (doc.isObject()) {
        QJsonObject obj = doc.object();
        result.success = obj.value("Result").toInt() == 1;
        result.message = obj.value("Err").toString();
        result.token = obj.value("Token").toString();
        result.userId = QString::number(obj.value("UserID").toInt());
        result.uid = obj.contains("UID") ? obj.value("UID").toVariant().toString() : QString();
    } else {
        result.success = false;
        result.message = "响应格式错误";
    }

    // 在主线程中处理结果
    QMetaObject::invokeMethod(this, [this, result, callback]() {
        onTaskCompleted(result);
        if (callback) {
            callback(result.accountIndex, result.success, result.message);
        }
    }, Qt::QueuedConnection);
}

void AsyncLoginManager::onTaskCompleted(const LoginResult& result)
{
    if (m_stopped) {
        return;
    }

    // 更新统计
    int completed = m_completedTasks.fetchAndAddAcquire(1) + 1;
    if (result.success) {
        m_successfulTasks.fetchAndAddAcquire(1);
    }

    // 发送信号
    emit accountLoginCompleted(result);
    emit progressUpdated(completed, m_totalTasks);

    // 检查是否全部完成
    if (completed >= m_totalTasks) {
        emit batchLoginCompleted(m_successfulTasks.loadAcquire(), m_totalTasks.loadAcquire());
    }
}
