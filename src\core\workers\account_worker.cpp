#include "account_worker.h"
#include "../../legacy/network/ultrafasttls.h"
#include "../utils/logger.h"
#include <QDateTime>
#include <QCryptographicHash>
#include <QUrlQuery>
#include <QUrl>

AccountWorker::AccountWorker(const QString& accountId, QObject* parent)
    : QObject(parent), m_accountId(accountId)
{
    // 创建固定的UltraFastTLS实例
    m_tlsInstance = new UltraFastTLS();
    m_tlsInstance->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);
    m_tlsInstance->setQuietMode(true);
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, QString("AccountWorker created for account: %1").arg(accountId));
}

AccountWorker::~AccountWorker()
{
    delete m_tlsInstance;
    NEW_LOG_INFO(NewLogCategory::SYSTEM, QString("AccountWorker destroyed for account: %1").arg(m_accountId));
}

void AccountWorker::setupAccount(const QString& username, const QString& password,
                                const QString& token, const QString& userId)
{
    m_username = username;
    m_password = password;
    m_token = token;
    m_userId = userId;
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, QString("Account %1 configured").arg(username));
}

void AccountWorker::setupProxy(const QString& host, int port, const QString& type,
                              const QString& user, const QString& pass)
{
    if (!host.isEmpty() && port > 0) {
        // 只设置一次代理，后续重复使用
        m_tlsInstance->setProxy(host, port, type, user, pass);
        m_proxyConfigured = true;
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, 
                    QString("Proxy configured for account %1: %2:%3 (%4)")
                    .arg(m_accountId, host).arg(port).arg(type));
    }
}

void AccountWorker::setGameId(const QString& gameId)
{
    m_gameId = gameId;
}

void AccountWorker::setPageSize(int pageSize)
{
    m_pageSize = pageSize;
}

void AccountWorker::setPriceFilter(const QString& priceStr)
{
    m_priceFilter = priceStr;
}

void AccountWorker::setFocusFlag(int focusFlag)
{
    m_focusFlag = focusFlag;
}

void AccountWorker::startRefresh()
{
    try {
        NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("Starting refresh for account: %1").arg(m_accountId));
        
        // 构建请求参数
        QList<QPair<QString, QString>> params = {
            {"IsPub", "0"},
            {"GameID", m_gameId},
            {"ZoneID", "0"},
            {"ServerID", "0"},
            {"SearchStr", ""},
            {"STier", ""},
            {"ETier", ""},
            {"Sort_Str", ""},
            {"PageIndex", "1"},
            {"PageSize", QString::number(m_pageSize)},
            {"Price_Str", m_priceFilter},
            {"PubCancel", "0"},
            {"SettleHour", "0"},
            {"FilterType", "0"},
            {"PGType", "0"},
            {"Focused", QString::number(m_focusFlag)},
            {"OrderType", "0"},
            {"PubRecommend", "0"},
            {"Score1", "0"},
            {"Score2", "0"},
            {"UserID", m_userId},
            {"TimeStamp", QString::number(QDateTime::currentSecsSinceEpoch())},
            {"Ver", "1.0"},
            {"AppVer", "5.0.6"},
            {"AppOS", "WebApp%20IOS"},
            {"AppID", "webapp"}
        };
        
        // 添加价格筛选参数
        if (!m_priceFilter.isEmpty()) {
            params.append({"PriceStr", m_priceFilter});
        }
        
        // 添加关注标志
        if (m_focusFlag != -1) {
            params.append({"IsFocused", QString::number(m_focusFlag)});
        }
        
        // 添加token
        if (!m_token.isEmpty()) {
            params.append({"Token", m_token});
        }
        
        // 计算签名
        QString action = "LevelOrderList";
        QString sign = calculateSign(action, params, m_token);
        params.append({"Sign", sign});
        
        // 构建POST数据
        QString postData = buildPostData(params);
        QString url = "https://server.dailiantong.com.cn/API/AppService.ashx?Action=LevelOrderList";
        
        // 使用固定的TLS实例执行请求（连接复用）
        QString response = m_tlsInstance->executeRequest(url, postData);
        
        if (!response.isEmpty()) {
            NEW_LOG_DEBUG(NewLogCategory::ORDER, QString("Account %1 refresh completed").arg(m_accountId));
            emit refreshCompleted(m_accountId, response, true);
        } else {
            NEW_LOG_ERROR(NewLogCategory::ORDER, QString("Account %1 refresh failed: empty response").arg(m_accountId));
            emit refreshError(m_accountId, "响应为空");
        }
        
    } catch (const std::exception& e) {
        QString error = QString("异常: %1").arg(e.what());
        NEW_LOG_ERROR(NewLogCategory::ORDER, QString("Account %1 refresh exception: %2").arg(m_accountId, error));
        emit refreshError(m_accountId, error);
    } catch (...) {
        QString error = "未知异常";
        NEW_LOG_ERROR(NewLogCategory::ORDER, QString("Account %1 refresh unknown exception").arg(m_accountId));
        emit refreshError(m_accountId, error);
    }
}

QString AccountWorker::buildPostData(const QList<QPair<QString, QString>>& params)
{
    QStringList parts;
    for (const auto& param : params) {
        parts.append(QString("%1=%2").arg(param.first, param.second));
    }
    return parts.join("&");
}

QString AccountWorker::calculateSign(const QString& action, const QList<QPair<QString, QString>>& params, const QString& token)
{
    // 使用与OrderAPI相同的签名算法
    const QString secretKey = "9c7b9399680658d308691f2acad58c0a";

    // 构建POST数据字符串
    QString data = buildPostData(params);

    // 解析参数并构建签名字符串
    QStringList pairs = data.split('&');
    QString unescapedData;
    for (const QString& pair : pairs) {
        QStringList keyValue = pair.split('=');
        if (keyValue.size() == 2) {
            QString key = QUrl::fromPercentEncoding(keyValue[0].toUtf8());
            QString value = QUrl::fromPercentEncoding(keyValue[1].toUtf8());
            unescapedData += key + value;
        }
    }

    QString signStr = secretKey + action + unescapedData + token;

    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(signStr.toUtf8());
    return hash.result().toHex().toLower();
}
