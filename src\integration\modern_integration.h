#pragma once

#include <QObject>
#include <memory>

// 前向声明
class MainWindow;
class NetworkManager;
class LegacyApiAdapter;
class AppController;
class OrderService;
class UltraFastTLSAdapter;
class OrderInfo;
class MainAccountInfo;
class AccountInfo;

class ModernIntegration : public QObject
{
    Q_OBJECT

public:
    explicit ModernIntegration(MainWindow* mainWindow, QObject* parent = nullptr);
    ~ModernIntegration() = default;  // 智能指针自动管理内存

    bool initialize();
    void shutdown();
    bool isInitialized() const { return m_initialized; }

    // 访问器（返回原始指针以保持兼容性）
    LegacyApiAdapter* getLegacyAdapter() const { return m_legacyApiAdapter.get(); }
    NetworkManager* getNetworkManager() const { return m_networkManager.get(); }

    // 业务操作方法
    bool startOrderRefresh();
    void stopOrderRefresh();
    bool loginMainAccount(const QString& username, const QString& password);
    void logoutAllAccounts();

signals:
    // 集成层信号
    void integrationError(const QString& error);
    void ordersUpdated(const QList<OrderInfo>& orders);
    void refreshStatusChanged(bool isRefreshing);
    void accountLoggedIn(const QString& username);

private slots:
    // AppController信号处理
    void onAppControllerInitialized();
    void onAppControllerError(const QString& error);
    void onOrdersRefreshed(const QList<OrderInfo>& orders);
    void onMainAccountLoggedIn(const MainAccountInfo& account);
    void onSubAccountLoggedIn(const AccountInfo& account);

private:
    void setupNetworkManager();
    void setupLegacyApiAdapter();
    void setupAppController();
    void connectSignals();

    MainWindow* m_mainWindow = nullptr;
    bool m_initialized = false;

    // 使用智能指针管理组件
    std::unique_ptr<NetworkManager> m_networkManager;
    std::unique_ptr<LegacyApiAdapter> m_legacyApiAdapter;
    std::unique_ptr<AppController> m_appController;
    std::unique_ptr<OrderService> m_orderService;
    std::unique_ptr<UltraFastTLSAdapter> m_tlsAdapter;
};
