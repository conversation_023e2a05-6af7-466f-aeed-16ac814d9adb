#ifndef FILTERWORKER_H
#define FILTERWORKER_H

#include <QObject>
#include <QSet>
#include <QString>
#include <QHash>

class FilterWorker : public QObject {
    Q_OBJECT // 确保Q_OBJECT宏在类定义的开始

public:
    explicit FilterWorker(QObject *parent = nullptr);
    
    // 设置排除关键词映射，格式为 "keyword/exclusion1 exclusion2 exclusion3..."
    void setExclusionMap(const QString &formatString);
    
public slots:
    // 处理额外排除关键词的过滤任务
    void processExtraExclusions(const QString &title);
    
signals:
    void resultReady(bool result);
    
private:
    QHash<QString, QSet<QString>> m_keywordExclusions; // 关键词到排除词的映射
};

#endif // FILTERWORKER_H 