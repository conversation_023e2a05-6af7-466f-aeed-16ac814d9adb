#include "json_parser.h"
#include "simple_logger.h"
#include <QJsonDocument>
#include <QJsonParseError>

JsonParser::JsonParser(QObject* parent)
    : QObject(parent)
{
}

JsonParser::ParseResult JsonParser::parseJson(const QString& jsonString) const
{
    // 使用早期返回和结构化绑定简化代码
    if (jsonString.isEmpty()) {
        return {false, {}, "JSON字符串为空"};
    }

    QJsonParseError parseError;
    auto doc = QJsonDocument::fromJson(jsonString.toUtf8(), &parseError);

    // 使用三元运算符简化错误处理
    return parseError.error != QJsonParseError::NoError ? ParseResult{false, {}, "JSON解析失败: " + parseError.errorString()}
         : !doc.isObject() ? ParseResult{false, {}, "JSON不是有效的对象"}
         : ParseResult{true, doc.object(), ""};
}

JsonParser::UserInfo JsonParser::parseUserInfo(const QString& jsonString)
{
    LOG_DEBUG(LogCategory::API, "开始解析用户信息");
    
    ParseResult parseResult = parseJson(jsonString);
    if (!parseResult.isSuccess()) {
        emit parseError("用户信息解析失败: " + parseResult.errorMessage);
        return UserInfo();
    }
    
    UserInfo userInfo = parseUserInfoFromObject(parseResult.data);
    
    if (userInfo.isValid()) {
        LOG_INFO(LogCategory::API, QString("用户信息解析成功: %1").arg(userInfo.userId));
        emit parseSuccess("用户信息");
    } else {
        LOG_WARNING(LogCategory::API, "用户信息解析结果无效");
        emit parseError("用户信息数据无效");
    }
    
    return userInfo;
}

QList<JsonParser::OrderInfo> JsonParser::parseOrderList(const QString& jsonString)
{
    LOG_DEBUG(LogCategory::API, "开始解析订单列表");
    
    QList<OrderInfo> orders;
    
    ParseResult parseResult = parseJson(jsonString);
    if (!parseResult.isSuccess()) {
        emit parseError("订单列表解析失败: " + parseResult.errorMessage);
        return orders;
    }
    
    QJsonObject obj = parseResult.data;
    if (!obj.contains("LevelOrderList")) {
        logParseError("订单列表", "缺少LevelOrderList字段");
        return orders;
    }
    
    QJsonArray orderArray = obj["LevelOrderList"].toArray();
    for (const QJsonValue& value : orderArray) {
        if (value.isObject()) {
            OrderInfo order = parseOrderInfoFromObject(value.toObject());
            if (order.isValid()) {
                orders.append(order);
            }
        }
    }
    
    LOG_INFO(LogCategory::API, QString("订单列表解析成功，共%1条订单").arg(orders.size()));
    emit parseSuccess("订单列表");
    
    return orders;
}

QString JsonParser::extractString(const QJsonObject& obj, const QString& key, const QString& defaultValue) const
{
    return obj.contains(key) && obj[key].isString() ? obj[key].toString() : defaultValue;
}

int JsonParser::extractInt(const QJsonObject& obj, const QString& key, int defaultValue) const
{
    if (!obj.contains(key)) return defaultValue;

    const auto& value = obj[key];
    return value.isDouble() ? value.toInt()
         : value.isString() ? value.toString().toInt() // 自动处理转换失败
         : defaultValue;
}

double JsonParser::extractDouble(const QJsonObject& obj, const QString& key, double defaultValue) const
{
    if (!obj.contains(key)) return defaultValue;

    const auto& value = obj[key];
    return value.isDouble() ? value.toDouble()
         : value.isString() ? value.toString().toDouble() // 自动处理转换失败
         : defaultValue;
}

bool JsonParser::extractBool(const QJsonObject& obj, const QString& key, bool defaultValue) const
{
    return obj.contains(key) && obj[key].isBool() ? obj[key].toBool() : defaultValue;
}

bool JsonParser::isValidJson(const QString& jsonString) const
{
    if (jsonString.isEmpty()) {
        return false;
    }
    
    QJsonParseError parseError;
    QJsonDocument::fromJson(jsonString.toUtf8(), &parseError);
    
    return parseError.error == QJsonParseError::NoError;
}

bool JsonParser::hasRequiredFields(const QJsonObject& obj, const QStringList& requiredFields) const
{
    for (const QString& field : requiredFields) {
        if (!obj.contains(field)) {
            return false;
        }
    }
    return true;
}

JsonParser::UserInfo JsonParser::parseUserInfoFromObject(const QJsonObject& obj) const
{
    UserInfo userInfo;
    
    userInfo.userId = extractString(obj, "UserID");
    if (userInfo.userId.isEmpty()) {
        userInfo.userId = QString::number(extractInt(obj, "UserID"));
    }
    
    userInfo.loginId = extractString(obj, "LoginID");
    userInfo.token = extractString(obj, "Token");
    userInfo.nickname = extractString(obj, "NickName");
    userInfo.mobile = extractString(obj, "BindMobile");
    userInfo.balance = extractDouble(obj, "SumBal");
    userInfo.hasPayPassword = extractInt(obj, "HavePayPass") == 1;
    
    return userInfo;
}

JsonParser::OrderInfo JsonParser::parseOrderInfoFromObject(const QJsonObject& obj) const
{
    OrderInfo orderInfo;
    
    orderInfo.serialNo = extractString(obj, "SerialNo");
    orderInfo.title = extractString(obj, "Title");
    orderInfo.price = extractDouble(obj, "Price");
    orderInfo.publisherUserId = extractString(obj, "UserID");
    if (orderInfo.publisherUserId.isEmpty()) {
        orderInfo.publisherUserId = QString::number(extractInt(obj, "UserID"));
    }
    orderInfo.gameId = extractString(obj, "GameID");
    orderInfo.zoneServerId = extractString(obj, "ZoneServerID");
    orderInfo.isPublic = extractInt(obj, "IsPub") == 1;
    
    return orderInfo;
}

void JsonParser::logParseError(const QString& context, const QString& error) const
{
    QString fullError = QString("[%1] %2").arg(context, error);
    LOG_ERROR(LogCategory::API, fullError);
}
