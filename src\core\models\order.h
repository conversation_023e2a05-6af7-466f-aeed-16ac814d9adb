#ifndef ORDER_H
#define ORDER_H

#include <QString>
#include <QDateTime>
#include <QJsonObject>
#include <QList>

/**
 * @brief 订单状态枚举
 */
enum class OrderStatus {
    AVAILABLE,      // 可接单
    ACCEPTED,       // 已接单
    IN_PROGRESS,    // 进行中
    COMPLETED,      // 已完成
    CANCELLED,      // 已取消
    EXPIRED         // 已过期
};

/**
 * @brief 订单优先级
 */
enum class OrderPriority {
    LOW = 1,
    NORMAL = 2,
    HIGH = 3,
    URGENT = 4
};

/**
 * @brief 新架构的订单信息数据模型 (避免与旧版本冲突)
 */
struct NewOrderInfo {
    QString serialNo;           // 订单号
    QString title;              // 订单标题
    double price = 0.0;         // 订单价格
    double ensure1 = 0.0;       // 保证金1
    double ensure2 = 0.0;       // 保证金2
    double ensure3 = 0.0;       // 保证金3
    QString gameId;             // 游戏ID
    QString zoneServerId;       // 区服ID
    QString userId;             // 发布用户ID
    int isPub = 0;              // 是否公开
    OrderStatus status = OrderStatus::AVAILABLE;
    OrderPriority priority = OrderPriority::NORMAL;
    QDateTime createTime;       // 创建时间
    QDateTime updateTime;       // 更新时间
    QDateTime expireTime;       // 过期时间
    
    // 扩展信息
    QString description;        // 订单描述
    QStringList tags;          // 订单标签
    QString contactInfo;       // 联系信息
    
    // 构造函数
    NewOrderInfo() = default;
    NewOrderInfo(const QString& serialNo, const QString& title, double price);

    // 从JSON创建
    static NewOrderInfo fromJson(const QJsonObject& json);
    QJsonObject toJson() const;
    
    // 工具方法
    bool isValid() const;
    bool isExpired() const;
    bool matchesKeywords(const QStringList& includeKeywords, 
                        const QStringList& excludeKeywords) const;
    bool matchesPriceRange(double minPrice, double maxPrice) const;
    QString getStatusString() const;
    QString getPriorityString() const;
    QString toString() const;
    
    // 比较操作符
    bool operator==(const NewOrderInfo& other) const;
    bool operator!=(const NewOrderInfo& other) const;
};

/**
 * @brief 订单过滤器
 */
struct OrderFilter {
    QStringList includeKeywords;
    QStringList excludeKeywords;
    double minPrice = 0.0;
    double maxPrice = 999999.0;
    QStringList gameIds;
    QStringList zoneServerIds;
    OrderStatus minStatus = OrderStatus::AVAILABLE;
    OrderStatus maxStatus = OrderStatus::EXPIRED;
    bool onlyPublic = false;
    
    // 过滤方法
    bool matches(const NewOrderInfo& order) const;
    QList<NewOrderInfo> filter(const QList<NewOrderInfo>& orders) const;
    
    // 序列化
    QJsonObject toJson() const;
    static OrderFilter fromJson(const QJsonObject& json);
};

/**
 * @brief 订单统计信息
 */
struct OrderStats {
    int totalOrders = 0;
    int availableOrders = 0;
    int acceptedOrders = 0;
    int completedOrders = 0;
    double totalValue = 0.0;
    double averagePrice = 0.0;
    double minPrice = 0.0;
    double maxPrice = 0.0;
    QDateTime lastUpdateTime;
    
    void reset();
    void update(const QList<NewOrderInfo>& orders);
    QString toString() const;
};

/**
 * @brief 订单管理器
 */
class OrderManager {
public:
    OrderManager() = default;
    
    // 订单管理
    void addOrder(const NewOrderInfo& order);
    void updateOrder(const NewOrderInfo& order);
    void removeOrder(const QString& serialNo);
    void clearOrders();

    // 订单查询
    NewOrderInfo getOrder(const QString& serialNo) const;
    QList<NewOrderInfo> getAllOrders() const;
    QList<NewOrderInfo> getFilteredOrders(const OrderFilter& filter) const;
    QList<NewOrderInfo> getOrdersByStatus(OrderStatus status) const;
    QList<NewOrderInfo> getOrdersByPriceRange(double minPrice, double maxPrice) const;

    // 订单搜索
    QList<NewOrderInfo> searchOrders(const QString& keyword) const;
    QList<NewOrderInfo> searchOrdersByTitle(const QString& title) const;
    
    // 统计信息
    OrderStats getStats() const;
    int getOrderCount() const;
    bool hasOrder(const QString& serialNo) const;
    
    // 排序
    enum SortBy {
        SORT_BY_PRICE,
        SORT_BY_CREATE_TIME,
        SORT_BY_UPDATE_TIME,
        SORT_BY_PRIORITY,
        SORT_BY_STATUS
    };
    QList<NewOrderInfo> getSortedOrders(SortBy sortBy, bool ascending = true) const;

private:
    QHash<QString, NewOrderInfo> m_orders;
    mutable OrderStats m_cachedStats;
    mutable bool m_statsValid = false;
    
    void invalidateStats();
};

#endif // ORDER_H
