#ifndef NETWORK_MANAGER_H
#define NETWORK_MANAGER_H

#include <QObject>
#include <QHash>
#include <QMutex>
#include <QTimer>
#include <memory>
#include "engines/network_engine_interface.h"

/**
 * @brief 网络性能统计
 */
struct NetworkPerformanceStats {
    int totalRequests = 0;
    int successfulRequests = 0;
    int failedRequests = 0;
    qint64 totalResponseTime = 0;
    qint64 minResponseTime = LLONG_MAX;
    qint64 maxResponseTime = 0;
    QDateTime startTime;
    
    double getSuccessRate() const {
        return totalRequests > 0 ? (double)successfulRequests / totalRequests * 100.0 : 0.0;
    }
    
    double getAverageResponseTime() const {
        return successfulRequests > 0 ? (double)totalResponseTime / successfulRequests : 0.0;
    }
    
    void reset() {
        totalRequests = 0;
        successfulRequests = 0;
        failedRequests = 0;
        totalResponseTime = 0;
        minResponseTime = LLONG_MAX;
        maxResponseTime = 0;
        startTime = QDateTime::currentDateTime();
    }
};

/**
 * @brief 网络管理器
 * 统一管理所有网络引擎，提供负载均衡、故障转移等功能
 */
class NetworkManager : public QObject {
    Q_OBJECT
    
public:
    explicit NetworkManager(QObject* parent = nullptr);
    ~NetworkManager();
    
    // 引擎管理
    bool addEngine(const QString& name, std::unique_ptr<INetworkEngine> engine);
    bool removeEngine(const QString& name);
    INetworkEngine* getEngine(const QString& name) const;  // 返回原始指针以保持接口兼容
    QStringList getAvailableEngines() const;
    
    // 主引擎设置
    bool setPrimaryEngine(const QString& name);
    QString getPrimaryEngine() const;
    bool setFallbackEngine(const QString& name);
    QString getFallbackEngine() const;
    
    // 网络请求
    NetworkResult executeRequest(const QString& url, 
                               const QString& postData,
                               const QJsonObject& headers,
                               const QString& engineName = "");
    
    // 异步请求
    void executeRequestAsync(const QString& url,
                           const QString& postData,
                           const QJsonObject& headers,
                           const QString& engineName = "");
    
    // 性能统计
    NetworkPerformanceStats getStats(const QString& engineName = "") const;
    QHash<QString, NetworkPerformanceStats> getAllStats() const;
    void resetStats(const QString& engineName = "");
    
    // 健康检查
    bool isEngineHealthy(const QString& engineName) const;  // 保持接口一致性
    void performHealthCheck();
    void setHealthCheckInterval(int intervalMs);
    
    // 负载均衡
    enum LoadBalanceStrategy {
        ROUND_ROBIN,
        LEAST_CONNECTIONS,
        FASTEST_RESPONSE,
        RANDOM
    };
    void setLoadBalanceStrategy(LoadBalanceStrategy strategy);
    QString selectBestEngine() const;
    
    // 故障转移
    void enableAutoFailover(bool enabled);
    void setFailoverThreshold(double errorRate); // 错误率阈值 (0.0-1.0)
    void setFailoverCooldown(int cooldownMs);

signals:
    void requestCompleted(const QString& engineName, const NetworkResult& result);
    void requestFailed(const QString& engineName, const QString& error);
    void engineHealthChanged(const QString& engineName, bool healthy);
    void engineAdded(const QString& engineName);
    void engineRemoved(const QString& engineName);
    void statsUpdated(const QString& engineName, const NetworkPerformanceStats& stats);

private slots:
    void onEngineRequestCompleted(const NetworkResult& result);
    void onEngineRequestFailed(const QString& error);
    void performHealthCheckInternal();

private:
    struct EngineInfo {
        std::unique_ptr<INetworkEngine> engine;
        NetworkPerformanceStats stats;
        bool healthy = true;
        QDateTime lastHealthCheck;
        int consecutiveFailures = 0;
        bool inCooldown = false;
        QDateTime cooldownEndTime;
    };
    
    void updateStats(const QString& engineName, const NetworkResult& result);
    void checkEngineHealth(const QString& engineName);
    QString selectEngineByStrategy() const;
    bool shouldFailover(const QString& engineName) const;
    void triggerFailover(const QString& engineName);
    
    QHash<QString, EngineInfo> m_engines;
    QString m_primaryEngine;
    QString m_fallbackEngine;
    LoadBalanceStrategy m_loadBalanceStrategy = FASTEST_RESPONSE;
    
    bool m_autoFailoverEnabled = true;
    double m_failoverThreshold = 0.5; // 50%错误率
    int m_failoverCooldown = 60000; // 1分钟冷却
    
    QTimer* m_healthCheckTimer = nullptr;
    int m_healthCheckInterval = 30000; // 30秒
    
    mutable QMutex m_mutex;
    int m_roundRobinIndex = 0;
};

#endif // NETWORK_MANAGER_H
