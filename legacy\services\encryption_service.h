#ifndef ENCRYPTION_SERVICE_H
#define ENCRYPTION_SERVICE_H

#include <QString>
#include <QObject>

/**
 * @brief 加密服务 - 专门负责数据加密和签名
 * 
 * 单一责任：只负责加密相关操作，不处理业务逻辑
 */
class EncryptionService : public QObject
{
    Q_OBJECT

public:
    explicit EncryptionService(QObject* parent = nullptr);
    
    // 核心职责：数据加密
    QString encryptPassword(const QString& password, const QString& loginId = "") const;
    QString generateSignature(const QString& action, const QString& data, const QString& token = "") const;
    QString generateMD5Hash(const QString& input) const;
    
    // 工具方法
    QString generateRandomString(int length) const;
    QString generateTimestamp() const;
    
    // 配置方法
    void setEncryptionKey(const QString& key) { m_encryptionKey = key; }
    void setSaltLength(int length) { m_saltLength = length; }

private:
    QString m_encryptionKey;
    int m_saltLength = 16;
    
    // 内部加密方法
    QString md5Hash(const QString& input) const;
    QString generateSalt(int length) const;
    QString combineForEncryption(const QString& password, const QString& salt, const QString& loginId) const;
};

#endif // ENCRYPTION_SERVICE_H
