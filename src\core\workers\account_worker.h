#pragma once

#include <QObject>
#include <QString>
#include <QDateTime>

// 前向声明
class UltraFastTLS;

/**
 * @brief 账号工作器 - 每个账号独立的刷新工作器
 * 
 * 特点：
 * - 每个账号有固定的UltraFastTLS实例
 * - 每个账号有固定的代理配置
 * - 运行在专用线程中，线程重复使用
 * - 享受连接复用的好处
 */
class AccountWorker : public QObject
{
    Q_OBJECT

public:
    explicit AccountWorker(const QString& accountId, QObject* parent = nullptr);
    ~AccountWorker();

    // 设置账号信息
    void setupAccount(const QString& username, const QString& password, 
                     const QString& token, const QString& userId);
    
    // 设置代理（只设置一次，后续重复使用）
    void setupProxy(const QString& host, int port, const QString& type,
                   const QString& user, const QString& pass);
    
    // 设置刷新参数
    void setGameId(const QString& gameId);
    void setPageSize(int pageSize);
    void setPriceFilter(const QString& priceStr);
    void setFocusFlag(int focusFlag);

public slots:
    void startRefresh();  // 开始刷新（在专用线程中执行）

signals:
    void refreshCompleted(QString accountId, QString result, bool success);
    void refreshError(QString accountId, QString error);

private:
    QString buildPostData(const QList<QPair<QString, QString>>& params);
    QString calculateSign(const QString& action, const QList<QPair<QString, QString>>& params, const QString& token);

    // 账号信息
    QString m_accountId;
    QString m_username;
    QString m_password;
    QString m_token;
    QString m_userId;
    
    // 刷新参数
    QString m_gameId = "110";
    int m_pageSize = 50;
    QString m_priceFilter;
    int m_focusFlag = -1;
    
    // 每个账号固定的UltraFastTLS实例
    UltraFastTLS* m_tlsInstance;
    bool m_proxyConfigured = false;
};
