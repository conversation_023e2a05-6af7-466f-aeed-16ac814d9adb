#ifndef ERROR_HANDLER_H
#define ERROR_HANDLER_H

#include <QString>
#include <QJsonObject>
#include <QJsonDocument>

/**
 * @brief 简单的错误处理工具类
 * 
 * 统一处理API响应中的错误，提供一致的错误信息格式
 */
class ErrorHandler
{
public:
    /**
     * @brief API响应结果结构
     */
    struct ApiResponse {
        bool success = false;
        QString message;
        int errorCode = 0;
        QJsonObject data;
        
        // 便利方法
        bool isSuccess() const { return success; }
        bool hasError() const { return !success; }
        QString getErrorMessage() const { return success ? "" : message; }
    };

    /**
     * @brief 解析API响应
     * @param response 原始响应字符串
     * @return 解析后的响应结果
     */
    static ApiResponse parseApiResponse(const QString& response);
    
    /**
     * @brief 解析登录响应
     * @param response 登录响应字符串
     * @return 解析后的响应结果
     */
    static ApiResponse parseLoginResponse(const QString& response);
    
    /**
     * @brief 解析订单响应
     * @param response 订单响应字符串
     * @return 解析后的响应结果
     */
    static ApiResponse parseOrderResponse(const QString& response);
    
    /**
     * @brief 检查响应是否为空
     * @param response 响应字符串
     * @param operation 操作名称（用于日志）
     * @return 是否有效
     */
    static bool validateResponse(const QString& response, const QString& operation);
    
    /**
     * @brief 格式化错误消息
     * @param operation 操作名称
     * @param error 错误信息
     * @param code 错误码
     * @return 格式化后的错误消息
     */
    static QString formatErrorMessage(const QString& operation, const QString& error, int code = 0);
    
    /**
     * @brief 获取友好的错误消息
     * @param errorCode 错误码
     * @return 用户友好的错误消息
     */
    static QString getFriendlyErrorMessage(int errorCode);

private:
    /**
     * @brief 解析JSON响应的通用方法
     * @param response 响应字符串
     * @param resultField 结果字段名（如"Result"）
     * @param messageField 消息字段名（如"Err"或"Message"）
     * @return 解析后的响应结果
     */
    static ApiResponse parseJsonResponse(const QString& response, 
                                       const QString& resultField = "Result",
                                       const QString& messageField = "Err");
};

/**
 * @brief 网络错误处理工具
 */
class NetworkErrorHandler
{
public:
    /**
     * @brief 网络错误类型
     */
    enum class ErrorType {
        TIMEOUT,        // 超时
        CONNECTION,     // 连接失败
        DNS,           // DNS解析失败
        SSL,           // SSL错误
        UNKNOWN        // 未知错误
    };
    
    /**
     * @brief 分析网络错误类型
     * @param errorMessage 错误消息
     * @return 错误类型
     */
    static ErrorType analyzeNetworkError(const QString& errorMessage);
    
    /**
     * @brief 获取网络错误的建议解决方案
     * @param errorType 错误类型
     * @return 建议解决方案
     */
    static QString getSuggestedSolution(ErrorType errorType);
    
    /**
     * @brief 判断是否应该重试
     * @param errorType 错误类型
     * @param retryCount 当前重试次数
     * @return 是否应该重试
     */
    static bool shouldRetry(ErrorType errorType, int retryCount);
};

// 便利宏定义
#define HANDLE_API_RESPONSE(response, operation) \
    ErrorHandler::parseApiResponse(response)

#define VALIDATE_RESPONSE(response, operation) \
    ErrorHandler::validateResponse(response, operation)

#define FORMAT_ERROR(operation, error, code) \
    ErrorHandler::formatErrorMessage(operation, error, code)

#endif // ERROR_HANDLER_H
