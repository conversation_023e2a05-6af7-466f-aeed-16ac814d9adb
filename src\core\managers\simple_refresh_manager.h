#pragma once

#include <QObject>
#include <QTimer>
#include <QThread>
#include <QList>
#include <QString>

// 前向声明
class AccountWorker;

/**
 * @brief 简单刷新管理器
 * 
 * 特点：
 * - 极简逻辑：串行刷新所有账号，然后固定间隔
 * - 每个账号有固定的线程和Worker，重复使用
 * - 每个账号有固定的UltraFastTLS实例和代理配置
 * - 固定间隔，不会跳过
 */
class SimpleRefreshManager : public QObject
{
    Q_OBJECT

public:
    explicit SimpleRefreshManager(QObject* parent = nullptr);
    ~SimpleRefreshManager();

    // 添加账号
    void addAccount(const QString& accountId, const QString& username, 
                   const QString& token, const QString& userId,
                   const QString& proxyHost, int proxyPort, 
                   const QString& proxyType, const QString& proxyUser, 
                   const QString& proxyPass);
    
    // 清除所有账号
    void clearAccounts();
    
    // 控制刷新
    void startBatchRefresh();
    void stopBatchRefresh();
    
    // 设置参数
    void setInterval(int seconds);
    void setGameId(const QString& gameId);
    void setPageSize(int pageSize);
    void setPriceFilter(const QString& priceStr);
    void setFocusFlag(int focusFlag);
    
    // 状态查询
    bool isRefreshing() const { return m_isRefreshing; }
    bool isInInterval() const { return m_isInInterval; }
    int getAccountCount() const { return m_accounts.size(); }
    int getCurrentProgress() const { return m_completedCount; }

signals:
    void accountRefreshCompleted(QString accountId, QString result);
    void accountRefreshError(QString accountId, QString error);
    void batchRefreshCompleted();
    void refreshProgress(int completed, int total);
    void intervalStarted(int seconds);
    void intervalFinished();

private slots:
    void onAccountCompleted(QString accountId, QString result, bool success);
    void onAccountError(QString accountId, QString error);
    void onIntervalTimeout();

private:
    void refreshNextAccount();
    void startInterval();
    void updateAccountParameters();

    struct AccountUnit {
        QString accountId;
        QThread* thread;        // 固定线程，重复使用
        AccountWorker* worker;  // 固定Worker
    };

    QList<AccountUnit> m_accounts;
    int m_currentAccountIndex = 0;
    int m_completedCount = 0;
    bool m_isRefreshing = false;
    bool m_isInInterval = false;

    // 定时器
    QTimer* m_intervalTimer;
    int m_intervalSeconds = 3;  // 默认3秒间隔
    
    // 刷新参数
    QString m_gameId = "110";
    int m_pageSize = 50;
    QString m_priceFilter;
    int m_focusFlag = -1;
};
