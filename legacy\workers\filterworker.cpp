#include "filterworker.h"
#include <QStringList>

FilterWorker::FilterWorker(QObject *parent) : QObject(parent) {
    // 不再在构造函数中初始化固定排除词
}

void FilterWorker::setExclusionMap(const QString &formatString) {
    // 解析格式为 "keyword/exclusion1 exclusion2 exclusion3..."
    // 或多个关键词格式: "keyword1/exclusion1 exclusion2;keyword2/exclusion3 exclusion4"
    m_keywordExclusions.clear();
    
    if (formatString.isEmpty()) return;
    
    // 支持多个关键词设置，使用分号分隔
    QStringList formatParts = formatString.split(';', Qt::SkipEmptyParts);
    
    // 如果没有分号，则视为单个格式
    if (formatParts.isEmpty() && formatString.contains('/')) {
        formatParts.append(formatString);
    }
    
    // 处理每个格式部分
    for (const QString &formatPart : formatParts) {
        if (formatPart.contains('/')) {
            QStringList parts = formatPart.split('/', Qt::SkipEmptyParts);
            if (parts.size() >= 2) {
                QString keyword = parts[0].trimmed();
                QString exclusions = parts[1].trimmed();
                QStringList exclusionList = exclusions.split(' ', Qt::SkipEmptyParts);
                
                // 使用QSet构造函数
                QSet<QString> exclusionSet;
                for (const QString &word : exclusionList) {
                    exclusionSet.insert(word);
                }
                
                if (!keyword.isEmpty() && !exclusionSet.isEmpty()) {
                    m_keywordExclusions[keyword] = exclusionSet;
                }
            }
        }
    }
}

void FilterWorker::processExtraExclusions(const QString &title) {
    bool result = true;
    
    // 检查标题是否包含任何关键词
    for (auto it = m_keywordExclusions.begin(); it != m_keywordExclusions.end(); ++it) {
        const QString &keyword = it.key();
        const QSet<QString> &exclusions = it.value();
        
        // 如果标题包含关键词，则检查排除词
        if (title.contains(keyword)) {
            // 检查是否包含任何排除关键词
            for (const QString &word : exclusions) {
                if (title.contains(word)) {
                    result = false;
                    break;
                }
            }
        }
        
        if (!result) break;
    }
    
    emit resultReady(result);
} 