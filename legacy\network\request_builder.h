#ifndef REQUEST_BUILDER_H
#define REQUEST_BUILDER_H

#include <QString>
#include <QHash>
#include <functional>

/**
 * @brief 请求构建器 - 专门负责构建各种类型的HTTP请求
 * 
 * 模块化设计：每个函数负责构建特定类型的请求
 */
class RequestBuilder
{
public:
    // ==================== 数据结构 ====================
    
    struct HttpRequest {
        QString url;
        QString method = "POST";
        QString postData;
        QHash<QString, QString> headers;
        int timeoutMs = 30000;
        
        bool isValid() const { return !url.isEmpty(); }
        bool isPost() const { return method.toUpper() == "POST"; }
        bool isGet() const { return method.toUpper() == "GET"; }
    };
    
    struct RequestConfig {
        QString userAgent;
        QString contentType;
        QString acceptLanguage;
        bool enableGzip;
        bool keepAlive;
        int maxRetries;

        // 构造函数设置默认值
        RequestConfig()
            : contentType("application/x-www-form-urlencoded; charset=UTF-8")
            , acceptLanguage("zh-CN,zh;q=0.9,en;q=0.8")
            , enableGzip(true)
            , keepAlive(true)
            , maxRetries(2)
        {}
    };

    // ==================== 构造函数 ====================

    explicit RequestBuilder(const RequestConfig& config = RequestConfig());
    RequestBuilder(); // 默认构造函数
    
    // ==================== 登录相关请求构建 ====================
    
    /**
     * @brief 构建预检验请求
     */
    HttpRequest buildPreCheckRequest(const QString& username, 
                                   const QString& encryptedPassword,
                                   const QString& gameId = "110") const;
    
    /**
     * @brief 构建登录请求
     */
    HttpRequest buildLoginRequest(const QString& username,
                                const QString& encryptedPassword,
                                const QString& loginId,
                                const QString& gameId = "110") const;
    
    // ==================== 订单相关请求构建 ====================
    
    /**
     * @brief 构建订单列表请求
     */
    HttpRequest buildOrderListRequest(const QString& token,
                                    const QString& userId,
                                    const QString& gameId = "110") const;
    
    /**
     * @brief 构建接单请求
     */
    HttpRequest buildAcceptOrderRequest(const QString& orderId,
                                      const QString& token,
                                      const QString& userId,
                                      const QString& payPassword,
                                      const QString& loginId) const;
    
    // ==================== 用户相关请求构建 ====================
    
    /**
     * @brief 构建用户信息请求
     */
    HttpRequest buildUserInfoRequest(const QString& token,
                                   const QString& userId) const;
    
    /**
     * @brief 构建余额查询请求
     */
    HttpRequest buildBalanceRequest(const QString& token,
                                  const QString& userId) const;
    
    // ==================== 通用请求构建 ====================
    
    /**
     * @brief 构建GET请求
     */
    HttpRequest buildGetRequest(const QString& url,
                              const QHash<QString, QString>& params = {}) const;
    
    /**
     * @brief 构建POST请求
     */
    HttpRequest buildPostRequest(const QString& url,
                               const QString& postData,
                               const QString& contentType = "") const;
    
    /**
     * @brief 构建带认证的请求
     */
    HttpRequest buildAuthenticatedRequest(const QString& action,
                                        const QString& token,
                                        const QString& userId,
                                        const QHash<QString, QString>& extraParams = {}) const;

private:
    RequestConfig m_config;
    
    // ==================== 内部辅助函数 ====================
    
    /**
     * @brief 添加默认请求头
     */
    void addDefaultHeaders(HttpRequest& request) const;
    
    /**
     * @brief 构建URL参数字符串
     */
    QString buildUrlParams(const QHash<QString, QString>& params) const;
    
    /**
     * @brief 构建POST数据
     */
    QString buildPostData(const QHash<QString, QString>& params) const;
    
    /**
     * @brief 添加设备信息参数
     */
    void addDeviceParams(QHash<QString, QString>& params) const;
    
    /**
     * @brief 添加认证参数
     */
    void addAuthParams(QHash<QString, QString>& params,
                      const QString& token,
                      const QString& userId) const;
    
    /**
     * @brief 构建API URL
     */
    QString buildApiUrl(const QString& action) const;
};

// ==================== 便利函数 ====================

/**
 * @brief 创建默认请求构建器
 */
RequestBuilder createDefaultRequestBuilder();

/**
 * @brief 创建移动端请求构建器
 */
RequestBuilder createMobileRequestBuilder();

/**
 * @brief 创建桌面端请求构建器
 */
RequestBuilder createDesktopRequestBuilder();

#endif // REQUEST_BUILDER_H
