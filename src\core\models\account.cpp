#include "account.h"
#include <QDebug>

// NewAccountInfo 实现
NewAccountInfo::NewAccountInfo(const QString& user, const QString& pass,
                              const QString& host, int port,
                              const QString& type,
                              const QString& proxyUser, const QString& proxyPass,
                              const QString& token, const QString& userId,
                              bool loggedIn, const QString& uid)
    : username(user)
    , password(pass)
    , proxyHost(host)
    , proxyPort(port)
    , proxyType(type)
    , proxyUser(proxyUser)
    , proxyPass(proxyPass)
    , token(token)
    , userId(userId)
    , isLoggedIn(loggedIn)
    , uid(uid)
    , loginSent(false)
    , api(nullptr)
    , lastLoginTime(QDateTime::currentDateTime())
{
}

bool NewAccountInfo::isValid() const
{
    return !username.isEmpty() && !password.isEmpty();
}

void NewAccountInfo::reset()
{
    token.clear();
    userId.clear();
    uid.clear();
    isLoggedIn = false;
    loginSent = false;
    lastLoginTime = QDateTime();

    // 注意：不重置 api 指针，因为它可能还在使用中
}

QString NewAccountInfo::toString() const
{
    return QString("NewAccountInfo{username=%1, isLoggedIn=%2, userId=%3, uid=%4}")
           .arg(username)
           .arg(isLoggedIn ? "true" : "false")
           .arg(userId)
           .arg(uid);
}

// NewMainAccountInfo 实现
NewMainAccountInfo::NewMainAccountInfo(const QString& user, const QString& pass,
                                      const QString& token, const QString& userId,
                                      const QString& uid, bool loggedIn)
    : username(user)
    , password(pass)
    , token(token)
    , userId(userId)
    , uid(uid)
    , isLoggedIn(loggedIn)
    , lastLoginTime(loggedIn ? QDateTime::currentDateTime() : QDateTime())
{
}

bool NewMainAccountInfo::isValid() const
{
    return !username.isEmpty() && !password.isEmpty();
}

void NewMainAccountInfo::reset()
{
    token.clear();
    userId.clear();
    uid.clear();
    isLoggedIn = false;
    lastLoginTime = QDateTime();
}
