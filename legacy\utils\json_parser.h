#ifndef JSON_PARSER_H
#define JSON_PARSER_H

#include <QString>
#include <QJsonObject>
#include <QJsonArray>
#include <QObject>

/**
 * @brief JSON解析器 - 专门负责JSON数据解析
 *
 * 单一责任：只负责JSON解析，不处理业务逻辑
 */
class JsonParser : public QObject
{
    Q_OBJECT

public:
    // ==================== 数据结构 ====================

    /**
     * @brief 解析结果
     */
    struct ParseResult {
        bool        success = false;
        QJsonObject data;
        QString     errorMessage;

        bool isSuccess() const  { return success; }
        bool hasData() const    { return success && !data.isEmpty(); }
    };

    /**
     * @brief 用户信息结构
     */
    struct UserInfo {
        QString userId;
        QString loginId;
        QString token;
        QString nickname;
        QString mobile;
        double  balance         = 0.0;
        bool    hasPayPassword  = false;

        bool isValid() const { return !userId.isEmpty() && !token.isEmpty(); }
    };

    /**
     * @brief 订单信息结构
     */
    struct OrderInfo {
        QString serialNo;
        QString title;
        double  price           = 0.0;
        QString publisherUserId;
        QString gameId;
        QString zoneServerId;
        bool    isPublic        = true;

        bool isValid() const { return !serialNo.isEmpty() && price > 0; }
    };

    // ==================== 构造函数 ====================

    explicit JsonParser(QObject* parent = nullptr);

    // ==================== 核心解析功能 ====================

    ParseResult parseJson(const QString& jsonString) const;

    // ==================== 专门解析方法 ====================

    UserInfo            parseUserInfo(const QString& jsonString);
    QList<OrderInfo>    parseOrderList(const QString& jsonString);

    // ==================== 通用提取方法 ====================

    QString extractString(const QJsonObject& obj, const QString& key,
                         const QString& defaultValue = "") const;

    int     extractInt(const QJsonObject& obj, const QString& key,
                      int defaultValue = 0) const;

    double  extractDouble(const QJsonObject& obj, const QString& key,
                         double defaultValue = 0.0) const;

    bool    extractBool(const QJsonObject& obj, const QString& key,
                       bool defaultValue = false) const;

    // ==================== 验证方法 ====================

    bool isValidJson(const QString& jsonString) const;
    bool hasRequiredFields(const QJsonObject& obj, const QStringList& requiredFields) const;

signals:
    void parseError(const QString& error);
    void parseSuccess(const QString& type);

private:
    // 内部解析方法
    UserInfo parseUserInfoFromObject(const QJsonObject& obj) const;
    OrderInfo parseOrderInfoFromObject(const QJsonObject& obj) const;
    void logParseError(const QString& context, const QString& error) const;
};

#endif // JSON_PARSER_H
