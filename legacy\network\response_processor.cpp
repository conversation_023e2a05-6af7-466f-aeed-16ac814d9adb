#include "response_processor.h"
#include "../utils/simple_logger.h"
#include <QJsonDocument>
#include <QJsonParseError>

// ==================== 登录响应处理 ====================

ResponseProcessor::ProcessResult ResponseProcessor::processPreCheckResponse(const QString& response) const
{
    ProcessResult result;
    
    if (!validateResponse(response)) {
        result.message = "预检验响应无效";
        return result;
    }
    
    QJsonObject obj = parseJsonResponse(response);
    if (obj.isEmpty()) {
        result.message = "预检验响应解析失败";
        return result;
    }
    
    // 提取LoginID
    QString loginId = extractString(obj, "LoginID");
    if (loginId.isEmpty()) {
        result.message = "无法获取LoginID";
        return result;
    }
    
    result.success = true;
    result.data = obj;
    result.message = "预检验成功";
    
    LOG_DEBUG(LogCategory::API, QString("预检验成功，LoginID: %1").arg(loginId));
    return result;
}

ResponseProcessor::LoginResult ResponseProcessor::processLoginResponse(const QString& response) const
{
    LoginResult result;
    
    if (!validateResponse(response)) {
        result.message = "登录响应无效";
        return result;
    }
    
    QJsonObject obj = parseJsonResponse(response);
    if (obj.isEmpty()) {
        result.message = "登录响应解析失败";
        return result;
    }
    
    // 检查登录结果
    int resultCode = extractInt(obj, "Result");
    if (resultCode != 1) {
        result.message = extractErrorMessage(obj);
        LOG_ERROR(LogCategory::LOGIN, QString("登录失败: %1").arg(result.message));
        return result;
    }
    
    // 提取用户信息
    result.success = true;
    result.userId = extractString(obj, "UserID");
    if (result.userId.isEmpty()) {
        result.userId = QString::number(extractInt(obj, "UserID"));
    }
    result.token = extractString(obj, "Token");
    result.loginId = extractString(obj, "LoginID");
    result.nickname = extractString(obj, "NickName");
    result.balance = extractDouble(obj, "SumBal");
    result.hasPayPassword = extractInt(obj, "HavePayPass") == 1;
    result.message = "登录成功";
    
    LOG_INFO(LogCategory::LOGIN, QString("登录成功，用户: %1").arg(result.nickname));
    return result;
}

// ==================== 订单响应处理 ====================

QList<ResponseProcessor::OrderInfo> ResponseProcessor::processOrderListResponse(const QString& response) const
{
    QList<OrderInfo> orders;
    
    if (!validateResponse(response)) {
        LOG_ERROR(LogCategory::ORDER, "订单列表响应无效");
        return orders;
    }
    
    QJsonObject obj = parseJsonResponse(response);
    if (obj.isEmpty()) {
        LOG_ERROR(LogCategory::ORDER, "订单列表响应解析失败");
        return orders;
    }
    
    // 检查响应结果
    if (!isResponseSuccessful(obj)) {
        LOG_ERROR(LogCategory::ORDER, QString("订单列表获取失败: %1").arg(extractErrorMessage(obj)));
        return orders;
    }
    
    // 解析订单数组
    QJsonArray orderArray = obj["LevelOrderList"].toArray();
    for (const QJsonValue& value : orderArray) {
        if (value.isObject()) {
            OrderInfo order = processOrderInfo(value.toObject());
            if (order.isValid()) {
                orders.append(order);
            }
        }
    }
    
    LOG_INFO(LogCategory::ORDER, QString("订单列表解析成功，共%1条订单").arg(orders.size()));
    return orders;
}

ResponseProcessor::ProcessResult ResponseProcessor::processAcceptOrderResponse(const QString& response) const
{
    ProcessResult result;
    
    if (!validateResponse(response)) {
        result.message = "接单响应无效";
        return result;
    }
    
    QJsonObject obj = parseJsonResponse(response);
    if (obj.isEmpty()) {
        result.message = "接单响应解析失败";
        return result;
    }
    
    // 检查接单结果
    int resultCode = extractInt(obj, "Result");
    result.success = (resultCode == 1);
    result.errorCode = resultCode;
    result.message = result.success ? "接单成功" : extractErrorMessage(obj);
    result.data = obj;
    
    LOG_INFO(LogCategory::ORDER, QString("接单结果: %1 - %2").arg(result.success ? "成功" : "失败", result.message));
    return result;
}

// ==================== 用户响应处理 ====================

ResponseProcessor::UserInfo ResponseProcessor::processUserInfoResponse(const QString& response) const
{
    UserInfo userInfo;
    
    if (!validateResponse(response)) {
        LOG_ERROR(LogCategory::API, "用户信息响应无效");
        return userInfo;
    }
    
    QJsonObject obj = parseJsonResponse(response);
    if (obj.isEmpty()) {
        LOG_ERROR(LogCategory::API, "用户信息响应解析失败");
        return userInfo;
    }
    
    // 检查响应结果
    if (!isResponseSuccessful(obj)) {
        LOG_ERROR(LogCategory::API, QString("用户信息获取失败: %1").arg(extractErrorMessage(obj)));
        return userInfo;
    }
    
    userInfo = processUserBasicInfo(obj);
    
    if (userInfo.isValid()) {
        LOG_INFO(LogCategory::API, QString("用户信息解析成功: %1").arg(userInfo.nickname));
    }
    
    return userInfo;
}

double ResponseProcessor::processBalanceResponse(const QString& response) const
{
    UserInfo userInfo = processUserInfoResponse(response);
    return userInfo.balance;
}

// ==================== 通用响应处理 ====================

ResponseProcessor::ProcessResult ResponseProcessor::processGenericResponse(const QString& response) const
{
    ProcessResult result;
    
    if (!validateResponse(response)) {
        result.message = "响应无效";
        return result;
    }
    
    QJsonObject obj = parseJsonResponse(response);
    if (obj.isEmpty()) {
        result.message = "响应解析失败";
        return result;
    }
    
    result.success = isResponseSuccessful(obj);
    result.errorCode = extractInt(obj, "Result");
    result.message = result.success ? "操作成功" : extractErrorMessage(obj);
    result.data = obj;
    
    return result;
}

bool ResponseProcessor::validateResponse(const QString& response) const
{
    return !response.trimmed().isEmpty() && response.length() > 5;
}

QString ResponseProcessor::extractErrorMessage(const QJsonObject& obj) const
{
    // 尝试多个可能的错误字段
    QString error = extractString(obj, "Err");
    if (!error.isEmpty()) return error;
    
    error = extractString(obj, "Message");
    if (!error.isEmpty()) return error;
    
    error = extractString(obj, "ErrorMessage");
    if (!error.isEmpty()) return error;
    
    // 根据错误码返回友好消息
    int errorCode = extractInt(obj, "Result");
    switch (errorCode) {
        case 1: return "操作成功";
        case 0: return "操作失败";
        case -1: return "网络连接失败";
        case -2: return "请求超时";
        case -3: return "数据格式错误";
        case -4: return "支付密码错误";
        case -5: return "参数错误";
        default: return QString("未知错误 (错误码: %1)").arg(errorCode);
    }
}

bool ResponseProcessor::isResponseSuccessful(const QJsonObject& obj) const
{
    return extractInt(obj, "Result") == 1;
}

// ==================== 内部辅助函数 ====================

QJsonObject ResponseProcessor::parseJsonResponse(const QString& response) const
{
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        LOG_ERROR(LogCategory::API, QString("JSON解析失败: %1").arg(parseError.errorString()));
        return QJsonObject();
    }
    
    if (!doc.isObject()) {
        LOG_ERROR(LogCategory::API, "响应不是有效的JSON对象");
        return QJsonObject();
    }
    
    return doc.object();
}

QString ResponseProcessor::extractString(const QJsonObject& obj, const QString& key, const QString& defaultValue) const
{
    return obj.contains(key) && obj[key].isString() ? obj[key].toString() : defaultValue;
}

int ResponseProcessor::extractInt(const QJsonObject& obj, const QString& key, int defaultValue) const
{
    if (!obj.contains(key)) return defaultValue;
    
    const QJsonValue& value = obj[key];
    return value.isDouble() ? value.toInt()
         : value.isString() ? value.toString().toInt()
         : defaultValue;
}

double ResponseProcessor::extractDouble(const QJsonObject& obj, const QString& key, double defaultValue) const
{
    if (!obj.contains(key)) return defaultValue;
    
    const QJsonValue& value = obj[key];
    return value.isDouble() ? value.toDouble()
         : value.isString() ? value.toString().toDouble()
         : defaultValue;
}

bool ResponseProcessor::extractBool(const QJsonObject& obj, const QString& key, bool defaultValue) const
{
    return obj.contains(key) && obj[key].isBool() ? obj[key].toBool() : defaultValue;
}

ResponseProcessor::OrderInfo ResponseProcessor::processOrderInfo(const QJsonObject& orderObj) const
{
    OrderInfo order;
    
    order.serialNo = extractString(orderObj, "SerialNo");
    order.title = extractString(orderObj, "Title");
    order.price = extractDouble(orderObj, "Price");
    order.publisherUserId = extractString(orderObj, "UserID");
    if (order.publisherUserId.isEmpty()) {
        order.publisherUserId = QString::number(extractInt(orderObj, "UserID"));
    }
    order.gameId = extractString(orderObj, "GameID");
    order.zoneServerId = extractString(orderObj, "ZoneServerID");
    order.isPublic = extractInt(orderObj, "IsPub") == 1;
    order.status = extractString(orderObj, "Status");
    order.createTime = extractString(orderObj, "CreateTime");
    
    return order;
}

ResponseProcessor::UserInfo ResponseProcessor::processUserBasicInfo(const QJsonObject& userObj) const
{
    UserInfo userInfo;
    
    userInfo.userId = extractString(userObj, "UserID");
    if (userInfo.userId.isEmpty()) {
        userInfo.userId = QString::number(extractInt(userObj, "UserID"));
    }
    userInfo.nickname = extractString(userObj, "NickName");
    userInfo.mobile = extractString(userObj, "BindMobile");
    userInfo.balance = extractDouble(userObj, "SumBal");
    userInfo.hasPayPassword = extractInt(userObj, "HavePayPass") == 1;
    userInfo.level = extractString(userObj, "Level");
    userInfo.orderCount = extractInt(userObj, "OrderCount");
    
    return userInfo;
}

QString ResponseProcessor::formatErrorMessage(const QString& operation, const QString& error, int code) const
{
    if (code != 0) {
        return QString("%1失败: %2 (错误码: %3)").arg(operation, error).arg(code);
    } else {
        return QString("%1失败: %2").arg(operation, error);
    }
}

// ==================== 便利函数 ====================

ResponseProcessor createDefaultResponseProcessor()
{
    return ResponseProcessor();
}

ResponseHandler<ResponseProcessor::LoginResult> createLoginResponseHandler()
{
    return [](const QString& response) {
        ResponseProcessor processor;
        return processor.processLoginResponse(response);
    };
}

ResponseHandler<QList<ResponseProcessor::OrderInfo>> createOrderListResponseHandler()
{
    return [](const QString& response) {
        ResponseProcessor processor;
        return processor.processOrderListResponse(response);
    };
}

ResponseHandler<ResponseProcessor::UserInfo> createUserInfoResponseHandler()
{
    return [](const QString& response) {
        ResponseProcessor processor;
        return processor.processUserInfoResponse(response);
    };
}
