#include "authentication_service.h"
#include "encryption_service.h"
#include "business_logic.h"  
#include "../utils/simple_logger.h"
#include "../utils/json_parser.h"
#include "../api/api_constants.h"

AuthenticationService::AuthenticationService(QObject* parent)
    : QObject(parent)
{
}

void AuthenticationService::loginAsync(const QString& username, const QString& password, 
                                      std::function<void(AuthResult)> callback)
{
    LOG_INFO(LogCategory::LOGIN, QString("开始异步登录: %1").arg(username));
    
    // 在后台线程执行登录（这里简化为同步调用）
    AuthResult result = loginSync(username, password);
    
    // 调用回调函数
    if (callback) {
        callback(result);
    }
    
    emit loginCompleted(result.success, result.message);
}

AuthenticationService::AuthResult AuthenticationService::loginSync(
    const QString& username,
    const QString& password)
{
    LOG_INFO(LogCategory::LOGIN, "开始同步登录: " + username);

    // ==================== 依赖检查 ====================

    if (!m_networkClient || !m_encryptionService || !m_jsonParser) {
        return {false, "认证服务未正确初始化", "", "", ""};
    }

    // ==================== 密码加密 ====================

    auto encryptedPassword = m_encryptionService->encryptPassword(password);
    if (encryptedPassword.isEmpty()) {
        return {false, "密码加密失败", "", "", ""};
    }

    // ==================== 登录流程 ====================

    // 步骤1: 预检验获取LoginID
    auto preCheckResult = performPreCheck(username, encryptedPassword);
    if (!preCheckResult.isSuccess()) {
        return preCheckResult;
    }

    // 步骤2: 执行登录
    auto loginResult = performLogin(username, encryptedPassword, preCheckResult.loginId);

    // 步骤3: 更新认证状态
    updateAuthenticationState(loginResult);

    return loginResult;
}

void AuthenticationService::clearAuthentication()
{
    LOG_INFO(LogCategory::LOGIN, "清除认证状态");
    
    m_currentToken.clear();
    m_currentUserId.clear();
    m_currentLoginId.clear();
    
    emit authenticationChanged(false);
}

AuthenticationService::AuthResult AuthenticationService::performPreCheck(const QString& username, const QString& encryptedPassword)
{
    AuthResult result;
    
    // 构建预检验请求
    QString url = buildApiUrl(ApiConstants::Actions::USER_TIP_FOR_CHANGE_PASS);
    QString postData = buildPreCheckData(username, encryptedPassword);
    
    LOG_DEBUG(LogCategory::LOGIN, "发送预检验请求");
    
    // 执行网络请求
    // NetworkClient已删除，需要使用其他网络实现
    QString networkResult = "NetworkClient已删除";
    if (networkResult.isEmpty()) {
        result.message = "预检验网络请求失败: NetworkClient已删除";
        LOG_ERROR(LogCategory::LOGIN, result.message);
        return result;
    }

    // 解析响应
    JsonParser::ParseResult parseResult = m_jsonParser->parseJson(networkResult);
    if (!parseResult.isSuccess()) {
        result.message = "预检验响应解析失败: " + parseResult.errorMessage;
        LOG_ERROR(LogCategory::LOGIN, result.message);
        return result;
    }
    
    // 提取LoginID
    QString loginId = m_jsonParser->extractString(parseResult.data, "LoginID");
    if (loginId.isEmpty()) {
        result.message = "预检验失败: 无法获取LoginID";
        LOG_ERROR(LogCategory::LOGIN, result.message);
        return result;
    }
    
    result.success = true;
    result.loginId = loginId;
    result.message = "预检验成功";
    
    LOG_INFO(LogCategory::LOGIN, QString("预检验成功，LoginID: %1").arg(loginId));
    
    return result;
}

AuthenticationService::AuthResult AuthenticationService::performLogin(const QString& username, const QString& encryptedPassword, const QString& loginId)
{
    AuthResult result;
    
    // 构建登录请求
    QString url = buildApiUrl(ApiConstants::Actions::GO_HOME);
    QString postData = buildLoginData(username, encryptedPassword, loginId);
    
    LOG_DEBUG(LogCategory::LOGIN, "发送登录请求");
    
    // 执行网络请求
    // NetworkClient已删除，需要使用其他网络实现
    QString networkResult = "NetworkClient已删除";
    if (networkResult.isEmpty()) {
        result.message = "登录网络请求失败: NetworkClient已删除";
        LOG_ERROR(LogCategory::LOGIN, result.message);
        return result;
    }

    // 解析用户信息
    JsonParser::UserInfo userInfo = m_jsonParser->parseUserInfo(networkResult);
    if (!userInfo.isValid()) {
        result.message = "登录失败: 用户信息无效";
        LOG_ERROR(LogCategory::LOGIN, result.message);
        return result;
    }
    
    result.success = true;
    result.userId = userInfo.userId;
    result.token = userInfo.token;
    result.loginId = userInfo.loginId;
    result.message = "登录成功";
    
    LOG_INFO(LogCategory::LOGIN, QString("登录成功，用户ID: %1").arg(userInfo.userId));
    
    return result;
}

void AuthenticationService::updateAuthenticationState(const AuthResult& result)
{
    if (result.isSuccess()) {
        m_currentToken = result.token;
        m_currentUserId = result.userId;
        m_currentLoginId = result.loginId;
        
        LOG_INFO(LogCategory::LOGIN, "认证状态已更新");
        emit authenticationChanged(true);
    } else {
        clearAuthentication();
    }
}

QString AuthenticationService::buildPreCheckData(const QString& username, const QString& encryptedPassword) const
{
    return QString("UserName=%1&Pass=%2&OS=WebApp&Channels=web&LoginType=1&GameID=%3&DeviceID=web_browser&AppVersion=1.0")
           .arg(username, encryptedPassword, ApiConstants::GameIds::NARUTO);
}

QString AuthenticationService::buildLoginData(const QString& username, const QString& encryptedPassword, const QString& loginId) const
{
    return QString("UserName=%1&Pass=%2&LoginID=%3&OS=WebApp&Channels=web&LoginType=1&GameID=%4&DeviceID=web_browser&AppVersion=1.0")
           .arg(username, encryptedPassword, loginId, ApiConstants::GameIds::NARUTO);
}
