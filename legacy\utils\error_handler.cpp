#include "error_handler.h"
#include "simple_logger.h"
#include <QJsonParseError>

ErrorHandler::ApiResponse ErrorHandler::parseApiResponse(const QString& response)
{
    return parseJsonResponse(response, "Result", "Err");
}

ErrorHandler::ApiResponse ErrorHandler::parseLoginResponse(const QString& response)
{
    ApiResponse result = parseJsonResponse(response, "Result", "Err");
    
    // 登录成功的特殊处理
    if (result.success && result.data.contains("Token")) {
        result.message = "登录成功";
    }
    
    return result;
}

ErrorHandler::ApiResponse ErrorHandler::parseOrderResponse(const QString& response)
{
    return parseJsonResponse(response, "Result", "Err");
}

bool ErrorHandler::validateResponse(const QString& response, const QString& operation)
{
    if (response.isEmpty()) {
        LOG_ERROR(LogCategory::API, QString("%1: 响应为空").arg(operation));
        return false;
    }
    
    if (response.length() < 10) {
        LOG_WARNING(LogCategory::API, QString("%1: 响应过短，可能无效").arg(operation));
        return false;
    }
    
    return true;
}

QString ErrorHandler::formatErrorMessage(const QString& operation, const QString& error, int code)
{
    if (code != 0) {
        return QString("%1失败: %2 (错误码: %3)").arg(operation, error).arg(code);
    } else {
        return QString("%1失败: %2").arg(operation, error);
    }
}

QString ErrorHandler::getFriendlyErrorMessage(int errorCode)
{
    switch (errorCode) {
        case 1: return "操作成功";
        case -1: return "网络连接失败，请检查网络设置";
        case -2: return "请求超时，请稍后重试";
        case -3: return "数据格式错误，请联系技术支持";
        case -4: return "支付密码错误，请重新输入";
        case -5: return "参数错误，请检查输入信息";
        case -6: return "权限不足，请重新登录";
        case -7: return "服务器繁忙，请稍后重试";
        case -8: return "账号已被锁定，请联系客服";
        case -9: return "余额不足，请先充值";
        case -10: return "订单已被接取";
        default: return QString("未知错误 (错误码: %1)").arg(errorCode);
    }
}

ErrorHandler::ApiResponse ErrorHandler::parseJsonResponse(const QString& response, 
                                                         const QString& resultField,
                                                         const QString& messageField)
{
    ApiResponse result;
    
    if (!validateResponse(response, "JSON解析")) {
        result.message = "响应数据无效";
        result.errorCode = -1;
        return result;
    }
    
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        LOG_ERROR(LogCategory::API, QString("JSON解析失败: %1").arg(parseError.errorString()));
        result.message = "数据格式错误";
        result.errorCode = -3;
        return result;
    }
    
    if (!doc.isObject()) {
        LOG_ERROR(LogCategory::API, "响应不是有效的JSON对象");
        result.message = "数据格式错误";
        result.errorCode = -3;
        return result;
    }
    
    QJsonObject obj = doc.object();
    result.data = obj;
    
    // 解析结果字段
    if (obj.contains(resultField)) {
        int resultCode = obj[resultField].toInt();
        result.success = (resultCode == 1);
        result.errorCode = resultCode;
        
        // 解析消息字段
        if (obj.contains(messageField)) {
            result.message = obj[messageField].toString();
        } else if (obj.contains("Message")) {
            result.message = obj["Message"].toString();
        } else {
            result.message = result.success ? "操作成功" : getFriendlyErrorMessage(resultCode);
        }
    } else {
        // 没有结果字段，假设成功
        result.success = true;
        result.message = "操作成功";
        result.errorCode = 1;
    }
    
    return result;
}

// NetworkErrorHandler 实现

NetworkErrorHandler::ErrorType NetworkErrorHandler::analyzeNetworkError(const QString& errorMessage)
{
    QString lowerError = errorMessage.toLower();
    
    if (lowerError.contains("timeout") || lowerError.contains("超时")) {
        return ErrorType::TIMEOUT;
    } else if (lowerError.contains("connection") || lowerError.contains("连接")) {
        return ErrorType::CONNECTION;
    } else if (lowerError.contains("dns") || lowerError.contains("域名")) {
        return ErrorType::DNS;
    } else if (lowerError.contains("ssl") || lowerError.contains("tls") || lowerError.contains("证书")) {
        return ErrorType::SSL;
    } else {
        return ErrorType::UNKNOWN;
    }
}

QString NetworkErrorHandler::getSuggestedSolution(ErrorType errorType)
{
    switch (errorType) {
        case ErrorType::TIMEOUT:
            return "网络超时，建议：1.检查网络连接 2.稍后重试 3.切换网络环境";
        case ErrorType::CONNECTION:
            return "连接失败，建议：1.检查网络设置 2.确认服务器地址 3.检查防火墙设置";
        case ErrorType::DNS:
            return "域名解析失败，建议：1.检查DNS设置 2.尝试使用其他DNS 3.检查网络连接";
        case ErrorType::SSL:
            return "SSL证书错误，建议：1.检查系统时间 2.更新证书 3.联系技术支持";
        case ErrorType::UNKNOWN:
        default:
            return "网络错误，建议：1.检查网络连接 2.重启应用 3.联系技术支持";
    }
}

bool NetworkErrorHandler::shouldRetry(ErrorType errorType, int retryCount)
{
    if (retryCount >= 3) {
        return false; // 最多重试3次
    }
    
    switch (errorType) {
        case ErrorType::TIMEOUT:
        case ErrorType::CONNECTION:
            return true; // 超时和连接错误可以重试
        case ErrorType::DNS:
            return retryCount < 2; // DNS错误最多重试2次
        case ErrorType::SSL:
            return false; // SSL错误通常不需要重试
        case ErrorType::UNKNOWN:
        default:
            return retryCount < 1; // 未知错误只重试1次
    }
}
