#include "request_builder.h"
#include "../api/api_constants.h"
#include <QUrl>
#include <QUrlQuery>

// ==================== 构造函数 ====================

RequestBuilder::RequestBuilder(const RequestConfig& config)
    : m_config(config)
{
    // 设置默认用户代理
    if (m_config.userAgent.isEmpty()) {
        m_config.userAgent = ApiConstants::UserAgents::QUARK_BROWSER;
    }
}

RequestBuilder::RequestBuilder()
{
    // RequestConfig的构造函数已经设置了默认值，只需要设置用户代理
    m_config.userAgent = ApiConstants::UserAgents::QUARK_BROWSER;
}

// ==================== 登录相关请求构建 ====================

RequestBuilder::HttpRequest RequestBuilder::buildPreCheckRequest(
    const QString& username, 
    const QString& encryptedPassword,
    const QString& gameId) const
{
    HttpRequest request;
    request.url = buildApiUrl(ApiConstants::Actions::USER_TIP_FOR_CHANGE_PASS);
    
    QHash<QString, QString> params;
    params["UserName"] = username;
    params["Pass"] = encryptedPassword;
    params["GameID"] = gameId;
    params["LoginType"] = "1";
    
    addDeviceParams(params);
    request.postData = buildPostData(params);
    addDefaultHeaders(request);
    
    return request;
}

RequestBuilder::HttpRequest RequestBuilder::buildLoginRequest(
    const QString& username,
    const QString& encryptedPassword,
    const QString& loginId,
    const QString& gameId) const
{
    HttpRequest request;
    request.url = buildApiUrl(ApiConstants::Actions::GO_HOME);
    
    QHash<QString, QString> params;
    params["UserName"] = username;
    params["Pass"] = encryptedPassword;
    params["LoginID"] = loginId;
    params["GameID"] = gameId;
    params["LoginType"] = "1";
    
    addDeviceParams(params);
    request.postData = buildPostData(params);
    addDefaultHeaders(request);
    
    return request;
}

// ==================== 订单相关请求构建 ====================

RequestBuilder::HttpRequest RequestBuilder::buildOrderListRequest(
    const QString& token,
    const QString& userId,
    const QString& gameId) const
{
    HttpRequest request;
    request.url = buildApiUrl(ApiConstants::Actions::LEVEL_ORDER_LIST);
    
    QHash<QString, QString> params;
    addAuthParams(params, token, userId);
    params["GameID"] = gameId;
    
    addDeviceParams(params);
    request.postData = buildPostData(params);
    addDefaultHeaders(request);
    
    return request;
}

RequestBuilder::HttpRequest RequestBuilder::buildAcceptOrderRequest(
    const QString& orderId,
    const QString& token,
    const QString& userId,
    const QString& payPassword,
    const QString& loginId) const
{
    HttpRequest request;
    request.url = buildApiUrl(ApiConstants::Actions::NEW_LEVEL_ORDER_ACCEPT);
    
    QHash<QString, QString> params;
    params["SerialNo"] = orderId;
    params["PayPass"] = payPassword;
    params["LoginID"] = loginId;
    
    addAuthParams(params, token, userId);
    addDeviceParams(params);
    
    request.postData = buildPostData(params);
    addDefaultHeaders(request);
    
    return request;
}

// ==================== 用户相关请求构建 ====================

RequestBuilder::HttpRequest RequestBuilder::buildUserInfoRequest(
    const QString& token,
    const QString& userId) const
{
    HttpRequest request;
    request.url = buildApiUrl(ApiConstants::Actions::USER_INFO_LIST);
    
    QHash<QString, QString> params;
    addAuthParams(params, token, userId);
    addDeviceParams(params);
    
    request.postData = buildPostData(params);
    addDefaultHeaders(request);
    
    return request;
}

RequestBuilder::HttpRequest RequestBuilder::buildBalanceRequest(
    const QString& token,
    const QString& userId) const
{
    // 余额查询通常包含在用户信息中
    return buildUserInfoRequest(token, userId);
}

// ==================== 通用请求构建 ====================

RequestBuilder::HttpRequest RequestBuilder::buildGetRequest(
    const QString& url,
    const QHash<QString, QString>& params) const
{
    HttpRequest request;
    request.method = "GET";
    
    if (params.isEmpty()) {
        request.url = url;
    } else {
        QString paramString = buildUrlParams(params);
        request.url = url + (url.contains('?') ? "&" : "?") + paramString;
    }
    
    addDefaultHeaders(request);
    return request;
}

RequestBuilder::HttpRequest RequestBuilder::buildPostRequest(
    const QString& url,
    const QString& postData,
    const QString& contentType) const
{
    HttpRequest request;
    request.url = url;
    request.method = "POST";
    request.postData = postData;
    
    addDefaultHeaders(request);
    
    if (!contentType.isEmpty()) {
        request.headers[ApiConstants::Headers::CONTENT_TYPE] = contentType;
    }
    
    return request;
}

RequestBuilder::HttpRequest RequestBuilder::buildAuthenticatedRequest(
    const QString& action,
    const QString& token,
    const QString& userId,
    const QHash<QString, QString>& extraParams) const
{
    HttpRequest request;
    request.url = buildApiUrl(action);
    
    QHash<QString, QString> params = extraParams;
    addAuthParams(params, token, userId);
    addDeviceParams(params);
    
    request.postData = buildPostData(params);
    addDefaultHeaders(request);
    
    return request;
}

// ==================== 内部辅助函数 ====================

void RequestBuilder::addDefaultHeaders(HttpRequest& request) const
{
    request.headers[ApiConstants::Headers::CONTENT_TYPE] = m_config.contentType;
    request.headers[ApiConstants::Headers::USER_AGENT] = m_config.userAgent;
    request.headers[ApiConstants::Headers::ACCEPT] = ApiConstants::Headers::DEFAULT_ACCEPT;
    request.headers[ApiConstants::Headers::ACCEPT_LANGUAGE] = m_config.acceptLanguage;
    
    if (m_config.enableGzip) {
        request.headers[ApiConstants::Headers::ACCEPT_ENCODING] = ApiConstants::Headers::DEFAULT_ACCEPT_ENCODING;
    }
    
    if (m_config.keepAlive) {
        request.headers[ApiConstants::Headers::CONNECTION] = ApiConstants::Headers::DEFAULT_CONNECTION;
    }
    
    request.headers[ApiConstants::Headers::CACHE_CONTROL] = ApiConstants::Headers::DEFAULT_CACHE_CONTROL;
}

QString RequestBuilder::buildUrlParams(const QHash<QString, QString>& params) const
{
    QStringList paramList;
    for (auto it = params.constBegin(); it != params.constEnd(); ++it) {
        paramList.append(QString("%1=%2").arg(
            QUrl::toPercentEncoding(it.key()),
            QUrl::toPercentEncoding(it.value())
        ));
    }
    return paramList.join("&");
}

QString RequestBuilder::buildPostData(const QHash<QString, QString>& params) const
{
    return buildUrlParams(params);  // POST数据格式与URL参数相同
}

void RequestBuilder::addDeviceParams(QHash<QString, QString>& params) const
{
    params["OS"] = "WebApp";
    params["Channels"] = "web";
    params["DeviceID"] = "web_browser";
    params["AppVersion"] = "1.0";
}

void RequestBuilder::addAuthParams(QHash<QString, QString>& params,
                                  const QString& token,
                                  const QString& userId) const
{
    params["Token"] = token;
    params["UserID"] = userId;
}

QString RequestBuilder::buildApiUrl(const QString& action) const
{
    return QString("%1?Action=%2").arg(ApiConstants::BASE_URL, action);
}

// ==================== 便利函数 ====================

RequestBuilder createDefaultRequestBuilder()
{
    RequestBuilder::RequestConfig config;
    config.userAgent = ApiConstants::UserAgents::QUARK_BROWSER;
    return RequestBuilder(config);
}

RequestBuilder createMobileRequestBuilder()
{
    RequestBuilder::RequestConfig config;
    config.userAgent = "Mozilla/5.0 (Linux; U; Android 15; zh-CN; V2307A Build/AP3A.240905.015.A1) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.80 Quark/7.14.5.880 Mobile Safari/537.36";
    return RequestBuilder(config);
}

RequestBuilder createDesktopRequestBuilder()
{
    RequestBuilder::RequestConfig config;
    config.userAgent = ApiConstants::UserAgents::CHROME;
    return RequestBuilder(config);
}
