#ifndef SIMPLE_LOGGER_H
#define SIMPLE_LOGGER_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QMutex>
#include <QFile>
#include <QTextStream>

/**
 * @brief 简单的日志记录类 (Legacy版本)
 * 与新的Logger系统并行运行，保持兼容性
 */
class SimpleLogger : public QObject
{
    Q_OBJECT

public:
    enum Level {
        DEBUG = 0,
        INFO = 1,
        WARNING = 2,
        LOG_ERROR = 3  // 避免与Windows ERROR宏冲突
    };

    static SimpleLogger* instance();
    
    // 日志级别设置
    void setLevel(Level level) { m_level = level; }
    Level getLevel() const { return m_level; }
    
    // 启用/禁用文件输出
    void enableFileOutput(bool enable) { m_fileOutputEnabled = enable; }
    void setLogFilePath(const QString& path) { m_logFilePath = path; }
    
    // 便利方法 (保持向后兼容)
    void debug(const QString& category, const QString& message);
    void info(const QString& category, const QString& message);
    void warning(const QString& category, const QString& message);
    void error(const QString& category, const QString& message);
    
    // 主要日志接口
    void log(Level level, const QString& category, const QString& message);

signals:
    void logMessage(const QString& message);

private:
    explicit SimpleLogger(QObject* parent = nullptr);
    static SimpleLogger* s_instance;
    static QMutex s_mutex;
    
    Level m_level = INFO;
    bool m_fileOutputEnabled = false;
    QString m_logFilePath;
    QString formatMessage(Level level, const QString& category, const QString& message);
    QString levelToString(Level level);
};

// 便捷宏定义 (Legacy系统使用)
#define SIMPLE_LOG_DEBUG(category, message) \
    SimpleLogger::instance()->debug(category, message)
#define SIMPLE_LOG_INFO(category, message) \
    SimpleLogger::instance()->info(category, message)
#define SIMPLE_LOG_WARNING(category, message) \
    SimpleLogger::instance()->warning(category, message)
#define SIMPLE_LOG_ERROR(category, message) \
    SimpleLogger::instance()->error(category, message)

// 旧版本兼容宏
#define LOG_DEBUG(category, message) \
    SimpleLogger::instance()->debug(category, message)
#define LOG_INFO(category, message) \
    SimpleLogger::instance()->info(category, message)
#define LOG_WARNING(category, message) \
    SimpleLogger::instance()->warning(category, message)
#define LOG_ERROR(category, message) \
    SimpleLogger::instance()->error(category, message)

// 常用分类 (保持向后兼容)
namespace LogCategory {
    const QString NETWORK = "Network";
    const QString API = "API";
    const QString LOGIN = "Login";
    const QString ORDER = "Order";
    const QString UI = "UI";
    const QString SYSTEM = "System";
    const QString SECURITY = "Security";
}

#endif // SIMPLE_LOGGER_H
