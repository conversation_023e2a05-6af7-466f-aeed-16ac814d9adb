#ifndef LOGGER_H
#define LOGGER_H

#include <QString>
#include <QObject>
#include <QMutex>
#include <QFile>
#include <QTextStream>
#include <QDateTime>

/**
 * @brief 日志级别
 */
enum class LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARNING = 2,
    LOG_ERROR = 3,    // 避免与Windows ERROR宏冲突
    CRITICAL = 4
};

/**
 * @brief 新日志类别（避免与现有SimpleLogger冲突）
 */
enum class NewLogCategory {
    SYSTEM,
    NETWORK,
    ORDER,
    LOGIN,
    API,
    UI,
    SECURITY
};

/**
 * @brief 统一日志管理器
 */
class Logger : public QObject {
    Q_OBJECT
    
public:
    // 单例模式
    static Logger& instance();
    
    // 日志配置
    void setLogLevel(LogLevel level);
    void setLogToFile(bool enabled, const QString& filePath = "");
    void setLogToConsole(bool enabled);
    void setMaxFileSize(qint64 maxSize); // 字节
    void setMaxFileCount(int maxCount);
    
    // 日志输出
    void log(LogLevel level, NewLogCategory category, const QString& message);
    void debug(NewLogCategory category, const QString& message);
    void info(NewLogCategory category, const QString& message);
    void warning(NewLogCategory category, const QString& message);
    void error(NewLogCategory category, const QString& message);
    void critical(NewLogCategory category, const QString& message);
    
    // 便捷方法
    void logNetwork(const QString& message);
    void logOrder(const QString& message);
    void logLogin(const QString& message);
    void logSystem(const QString& message);
    
    // 日志查询
    QStringList getRecentLogs(int count = 100) const;
    void clearLogs();
    
    // 性能统计
    struct LogStats {
        int debugCount = 0;
        int infoCount = 0;
        int warningCount = 0;
        int errorCount = 0;
        int criticalCount = 0;
        QDateTime startTime;
    };
    LogStats getStats() const;
    void resetStats();

signals:
    void logMessage(const QString& message);
    void logLevelChanged(LogLevel level);

private:
    Logger() = default;
    ~Logger();
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    void writeToFile(const QString& formattedMessage);
    void writeToConsole(const QString& formattedMessage);
    QString formatMessage(LogLevel level, NewLogCategory category, const QString& message) const;
    QString levelToString(LogLevel level) const;
    QString categoryToString(NewLogCategory category) const;
    void rotateLogFile();
    
    LogLevel m_logLevel = LogLevel::INFO;
    bool m_logToFile = false;
    bool m_logToConsole = true;
    QString m_logFilePath;
    qint64 m_maxFileSize = 10 * 1024 * 1024; // 10MB
    int m_maxFileCount = 5;
    
    mutable QMutex m_mutex;
    QFile* m_logFile = nullptr;
    QTextStream* m_logStream = nullptr;
    
    mutable QStringList m_recentLogs;
    static const int MAX_RECENT_LOGS = 1000;
    
    LogStats m_stats;
};

// 便捷宏定义（使用NEW_前缀避免冲突）
#define NEW_LOG_DEBUG(category, message) Logger::instance().debug(category, message)
#define NEW_LOG_INFO(category, message) Logger::instance().info(category, message)
#define NEW_LOG_WARNING(category, message) Logger::instance().warning(category, message)
#define NEW_LOG_ERROR(category, message) Logger::instance().error(category, message)
#define NEW_LOG_CRITICAL(category, message) Logger::instance().critical(category, message)

// 特定类别的便捷宏
#define NEW_LOG_NETWORK(message) Logger::instance().logNetwork(message)
#define NEW_LOG_ORDER(message) Logger::instance().logOrder(message)
#define NEW_LOG_LOGIN(message) Logger::instance().logLogin(message)
#define NEW_LOG_SYSTEM(message) Logger::instance().logSystem(message)

#endif // LOGGER_H
