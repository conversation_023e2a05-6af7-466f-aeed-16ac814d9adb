#include "app_controller.h"
#include "../services/order_service.h"
#include "../../network/network_manager.h"
#include "../utils/logger.h"
#include "../../config/app_config.h"
#include "../../mainwindow.h"  // 添加MainWindow的完整定义
#include <QTimer>

AppController::AppController(QObject* parent)
    : QObject(parent)
    , m_currentState(AppState::INITIALIZING)
    , m_startTime(QDateTime::currentDateTime())
    , m_statsUpdateTimer(new QTimer(this))
    , m_uptimeTimer(new QTimer(this))
{
    // 初始化统计定时器
    connect(m_statsUpdateTimer, &QTimer::timeout, this, &AppController::onStatsUpdateTimer);
    connect(m_uptimeTimer, &QTimer::timeout, this, &AppController::onUptimeTimer);
    
    m_statsUpdateTimer->setInterval(5000); // 每5秒更新统计
    m_uptimeTimer->setInterval(1000);      // 每秒更新运行时间
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "AppController created");
}

AppController::~AppController()
{
    shutdown();
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "AppController destroyed");
}

bool AppController::initialize()
{
    setState(AppState::INITIALIZING);
    
    try {
        // 初始化服务
        initializeServices();
        initializeNetworkManager();
        connectSignals();
        
        // 启动统计定时器
        m_statsUpdateTimer->start();
        m_uptimeTimer->start();
        
        setState(AppState::READY);
        emit initialized();
        
        NEW_LOG_INFO(NewLogCategory::SYSTEM, "AppController initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        QString error = QString("Failed to initialize AppController: %1").arg(e.what());
        NEW_LOG_ERROR(NewLogCategory::SYSTEM, error);
        handleCriticalError(error);
        return false;
    }
}

void AppController::shutdown()
{
    setState(AppState::SHUTTING_DOWN);
    
    // 停止所有定时器
    m_statsUpdateTimer->stop();
    m_uptimeTimer->stop();
    
    // 停止订单刷新
    if (m_orderService) {
        m_orderService->stopAutoRefresh();
    }
    
    // 清理服务
    if (m_orderService) {
        delete m_orderService;
        m_orderService = nullptr;
    }
    
    if (m_networkManager) {
        delete m_networkManager;
        m_networkManager = nullptr;
    }
    
    emit shutdownCompleted();
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "AppController shutdown completed");
}

void AppController::setMainWindow(MainWindow* mainWindow)
{
    m_mainWindow = mainWindow;
    NEW_LOG_INFO(NewLogCategory::UI, "MainWindow connected to AppController");
}

MainWindow* AppController::getMainWindow() const
{
    return m_mainWindow;
}

void AppController::showMainWindow()
{
    if (m_mainWindow) {
        m_mainWindow->show();
        m_mainWindow->raise();
        m_mainWindow->activateWindow();
    }
}

void AppController::hideMainWindow()
{
    if (m_mainWindow) {
        m_mainWindow->hide();
    }
}

bool AppController::loginMainAccount(const QString& username, const QString& password)
{
    if (m_currentState != AppState::READY) {
        NEW_LOG_ERROR(NewLogCategory::LOGIN, "Cannot login: AppController not ready");
        return false;
    }
    
    setState(AppState::LOGGING_IN);
    
    // 创建主账号信息
    MainAccountInfo account(username, password);
    
    // 这里应该调用实际的登录逻辑
    // 暂时模拟登录成功
    account.isLoggedIn = true;
    account.token = "mock_token";
    account.userId = "mock_user_id";
    account.lastLoginTime = QDateTime::currentDateTime();
    
    m_mainAccount = account;
    
    if (m_orderService) {
        m_orderService->setMainAccount(account);
    }
    
    setState(AppState::READY);
    emit mainAccountLoggedIn(account);
    
    NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Main account logged in: %1").arg(username));
    return true;
}

bool AppController::loginSubAccount(const AccountInfo& account)
{
    if (m_currentState != AppState::READY) {
        NEW_LOG_ERROR(NewLogCategory::LOGIN, "Cannot login: AppController not ready");
        return false;
    }
    
    // 这里应该调用实际的登录逻辑
    AccountInfo loggedInAccount = account;
    loggedInAccount.isLoggedIn = true;
    loggedInAccount.token = "mock_token";
    loggedInAccount.userId = "mock_user_id";
    loggedInAccount.lastLoginTime = QDateTime::currentDateTime();
    
    // 添加到子账号列表
    bool found = false;
    for (int i = 0; i < m_subAccounts.size(); ++i) {
        if (m_subAccounts[i].username == account.username) {
            m_subAccounts[i] = loggedInAccount;
            found = true;
            break;
        }
    }
    
    if (!found) {
        m_subAccounts.append(loggedInAccount);
    }
    
    if (m_orderService) {
        m_orderService->addSubAccount(loggedInAccount);
    }
    
    emit subAccountLoggedIn(loggedInAccount);
    
    NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Sub account logged in: %1").arg(account.username));
    return true;
}

void AppController::logoutMainAccount()
{
    m_mainAccount.reset();
    
    if (m_orderService) {
        m_orderService->setMainAccount(m_mainAccount);
    }
    
    emit mainAccountLoggedOut();
    NEW_LOG_INFO(NewLogCategory::LOGIN, "Main account logged out");
}

void AppController::logoutSubAccount(const QString& username)
{
    for (int i = 0; i < m_subAccounts.size(); ++i) {
        if (m_subAccounts[i].username == username) {
            m_subAccounts.removeAt(i);
            
            if (m_orderService) {
                m_orderService->removeSubAccount(username);
            }
            
            emit subAccountLoggedOut(username);
            NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Sub account logged out: %1").arg(username));
            return;
        }
    }
}

void AppController::logoutAllAccounts()
{
    // 登出主账号
    logoutMainAccount();
    
    // 登出所有子账号
    QStringList usernames;
    for (const AccountInfo& account : m_subAccounts) {
        usernames.append(account.username);
    }
    
    for (const QString& username : usernames) {
        logoutSubAccount(username);
    }
    
    NEW_LOG_INFO(NewLogCategory::LOGIN, "All accounts logged out");
}

void AppController::startBatchLogin(const QList<AccountInfo>& accounts)
{
    if (m_batchLoginRunning) {
        NEW_LOG_WARNING(NewLogCategory::LOGIN, "Batch login already running");
        return;
    }
    
    m_batchLoginRunning = true;
    m_batchLoginCurrent = 0;
    m_batchLoginTotal = accounts.size();
    
    // 这里应该实现实际的批量登录逻辑
    // 暂时模拟批量登录
    int successful = 0;
    for (const AccountInfo& account : accounts) {
        if (loginSubAccount(account)) {
            successful++;
        }
        m_batchLoginCurrent++;
        emit batchLoginProgress(m_batchLoginCurrent, m_batchLoginTotal);
    }
    
    m_batchLoginRunning = false;
    emit batchLoginCompleted(successful, m_batchLoginTotal);
    
    NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Batch login completed: %1/%2 successful")
                 .arg(successful).arg(m_batchLoginTotal));
}

void AppController::stopBatchLogin()
{
    if (m_batchLoginRunning) {
        m_batchLoginRunning = false;
        NEW_LOG_INFO(NewLogCategory::LOGIN, "Batch login stopped");
    }
}

bool AppController::isBatchLoginRunning() const
{
    return m_batchLoginRunning;
}

void AppController::startOrderRefresh()
{
    if (!m_orderService) {
        emit errorOccurred("OrderService not available");
        return;
    }
    
    setState(AppState::REFRESHING_ORDERS);
    m_orderService->startAutoRefresh();
    
    NEW_LOG_INFO(NewLogCategory::ORDER, "Order refresh started");
}

void AppController::stopOrderRefresh()
{
    if (m_orderService) {
        m_orderService->stopAutoRefresh();
    }
    
    if (m_currentState == AppState::REFRESHING_ORDERS) {
        setState(AppState::READY);
    }
    
    NEW_LOG_INFO(NewLogCategory::ORDER, "Order refresh stopped");
}

bool AppController::isOrderRefreshRunning() const
{
    return m_orderService ? m_orderService->isAutoRefreshing() : false;
}

void AppController::refreshOrdersOnce()
{
    if (m_orderService) {
        m_orderService->refreshOrders();
        NEW_LOG_INFO(NewLogCategory::ORDER, "Manual order refresh triggered");
    }
}

void AppController::acceptOrder(const QString& serialNo)
{
    // 这里应该实现订单接单逻辑
    NEW_LOG_INFO(NewLogCategory::ORDER, QString("Order accept requested: %1").arg(serialNo));
    
    // 模拟接单成功
    emit orderAccepted(serialNo, true);
}

void AppController::setOrderFilter(const OrderFilter& filter)
{
    if (m_orderService) {
        m_orderService->setOrderFilter(filter);
        NEW_LOG_INFO(NewLogCategory::ORDER, "Order filter updated");
    }
}

OrderFilter AppController::getOrderFilter() const
{
    return m_orderService ? m_orderService->getOrderFilter() : OrderFilter();
}

AppController::AppState AppController::getState() const
{
    return m_currentState;
}

QString AppController::getStateString() const
{
    switch (m_currentState) {
    case AppState::INITIALIZING: return "初始化中";
    case AppState::READY: return "就绪";
    case AppState::LOGGING_IN: return "登录中";
    case AppState::REFRESHING_ORDERS: return "刷新订单中";
    case AppState::SHUTTING_DOWN: return "关闭中";
    case AppState::ERROR: return "错误";
    default: return "未知";
    }
}

AppController::AppStats AppController::getStats() const
{
    AppStats stats;
    stats.loggedInAccounts = (m_mainAccount.isLoggedIn ? 1 : 0) + m_subAccounts.size();
    stats.uptime = m_startTime.secsTo(QDateTime::currentDateTime());
    
    if (m_orderService) {
        OrderStats orderStats = m_orderService->getOrderStats();
        stats.totalOrders = orderStats.totalOrders;
        stats.availableOrders = orderStats.availableOrders;
        stats.totalOrderValue = orderStats.totalValue;
        stats.lastRefreshTime = orderStats.lastUpdateTime;
    }
    
    return stats;
}
