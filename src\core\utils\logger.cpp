#include "logger.h"
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QCoreApplication>

Logger& Logger::instance()
{
    static Logger instance;
    return instance;
}

Logger::~Logger()
{
    if (m_logStream) {
        delete m_logStream;
    }
    if (m_logFile) {
        m_logFile->close();
        delete m_logFile;
    }
}

void Logger::setLogLevel(LogLevel level)
{
    QMutexLocker locker(&m_mutex);
    m_logLevel = level;
    emit logLevelChanged(level);
}

void Logger::setLogToFile(bool enabled, const QString& filePath)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_logStream) {
        delete m_logStream;
        m_logStream = nullptr;
    }
    if (m_logFile) {
        m_logFile->close();
        delete m_logFile;
        m_logFile = nullptr;
    }
    
    m_logToFile = enabled;
    if (enabled) {
        if (filePath.isEmpty()) {
            QString logDir = QStandardPaths::writableLocation(QStandardPaths::AppLocalDataLocation);
            QDir().mkpath(logDir);
            m_logFilePath = logDir + "/application.log";
        } else {
            m_logFilePath = filePath;
        }
        
        m_logFile = new QFile(m_logFilePath);
        if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
            m_logStream = new QTextStream(m_logFile);
            // Qt6默认使用UTF-8，不需要设置编码
        }
    }
}

void Logger::setLogToConsole(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_logToConsole = enabled;
}

void Logger::setMaxFileSize(qint64 maxSize)
{
    QMutexLocker locker(&m_mutex);
    m_maxFileSize = maxSize;
}

void Logger::setMaxFileCount(int maxCount)
{
    QMutexLocker locker(&m_mutex);
    m_maxFileCount = maxCount;
}

void Logger::log(LogLevel level, NewLogCategory category, const QString& message)
{
    if (level < m_logLevel) {
        return; // 日志级别过低，不输出
    }
    
    QMutexLocker locker(&m_mutex);
    
    QString formattedMessage = formatMessage(level, category, message);
    
    // 更新统计
    switch (level) {
    case LogLevel::DEBUG: m_stats.debugCount++; break;
    case LogLevel::INFO: m_stats.infoCount++; break;
    case LogLevel::WARNING: m_stats.warningCount++; break;
    case LogLevel::LOG_ERROR: m_stats.errorCount++; break;
    case LogLevel::CRITICAL: m_stats.criticalCount++; break;
    }
    
    // 添加到最近日志
    m_recentLogs.append(formattedMessage);
    if (m_recentLogs.size() > MAX_RECENT_LOGS) {
        m_recentLogs.removeFirst();
    }
    
    // 输出到文件
    if (m_logToFile) {
        writeToFile(formattedMessage);
    }
    
    // 输出到控制台
    if (m_logToConsole) {
        writeToConsole(formattedMessage);
    }
    
    // 发送信号
    emit logMessage(formattedMessage);
}

void Logger::debug(NewLogCategory category, const QString& message)
{
    log(LogLevel::DEBUG, category, message);
}

void Logger::info(NewLogCategory category, const QString& message)
{
    log(LogLevel::INFO, category, message);
}

void Logger::warning(NewLogCategory category, const QString& message)
{
    log(LogLevel::WARNING, category, message);
}

void Logger::error(NewLogCategory category, const QString& message)
{
    log(LogLevel::LOG_ERROR, category, message);
}

void Logger::critical(NewLogCategory category, const QString& message)
{
    log(LogLevel::CRITICAL, category, message);
}

void Logger::logNetwork(const QString& message)
{
    info(NewLogCategory::NETWORK, message);
}

void Logger::logOrder(const QString& message)
{
    info(NewLogCategory::ORDER, message);
}

void Logger::logLogin(const QString& message)
{
    info(NewLogCategory::LOGIN, message);
}

void Logger::logSystem(const QString& message)
{
    info(NewLogCategory::SYSTEM, message);
}

QStringList Logger::getRecentLogs(int count) const
{
    QMutexLocker locker(&m_mutex);
    if (count >= m_recentLogs.size()) {
        return m_recentLogs;
    }
    return m_recentLogs.mid(m_recentLogs.size() - count);
}

void Logger::clearLogs()
{
    QMutexLocker locker(&m_mutex);
    m_recentLogs.clear();
    
    if (m_logFile) {
        m_logFile->resize(0);
    }
}

Logger::LogStats Logger::getStats() const
{
    QMutexLocker locker(&m_mutex);
    return m_stats;
}

void Logger::resetStats()
{
    QMutexLocker locker(&m_mutex);
    m_stats = LogStats();
    m_stats.startTime = QDateTime::currentDateTime();
}

void Logger::writeToFile(const QString& formattedMessage)
{
    if (!m_logStream) return;
    
    // 检查文件大小，必要时轮转
    if (m_logFile->size() > m_maxFileSize) {
        rotateLogFile();
    }
    
    *m_logStream << formattedMessage << Qt::endl;
    m_logStream->flush();
}

void Logger::writeToConsole(const QString& formattedMessage)
{
    qDebug().noquote() << formattedMessage;
}

QString Logger::formatMessage(LogLevel level, NewLogCategory category, const QString& message) const
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString levelStr = levelToString(level);
    QString categoryStr = categoryToString(category);
    
    return QString("[%1] [%2] [%3] %4")
           .arg(timestamp)
           .arg(levelStr)
           .arg(categoryStr)
           .arg(message);
}

QString Logger::levelToString(LogLevel level) const
{
    switch (level) {
    case LogLevel::DEBUG: return "DEBUG";
    case LogLevel::INFO: return "INFO";
    case LogLevel::WARNING: return "WARN";
    case LogLevel::LOG_ERROR: return "ERROR";
    case LogLevel::CRITICAL: return "CRITICAL";
    default: return "UNKNOWN";
    }
}

QString Logger::categoryToString(NewLogCategory category) const
{
    switch (category) {
    case NewLogCategory::SYSTEM: return "System";
    case NewLogCategory::NETWORK: return "Network";
    case NewLogCategory::ORDER: return "Order";
    case NewLogCategory::LOGIN: return "Login";
    case NewLogCategory::API: return "API";
    case NewLogCategory::UI: return "UI";
    case NewLogCategory::SECURITY: return "Security";
    default: return "Unknown";
    }
}

void Logger::rotateLogFile()
{
    if (!m_logFile) return;
    
    m_logFile->close();
    
    // 轮转日志文件
    for (int i = m_maxFileCount - 1; i > 0; --i) {
        QString oldName = QString("%1.%2").arg(m_logFilePath).arg(i);
        QString newName = QString("%1.%2").arg(m_logFilePath).arg(i + 1);
        
        if (QFile::exists(oldName)) {
            QFile::remove(newName);
            QFile::rename(oldName, newName);
        }
    }
    
    // 重命名当前日志文件
    QString backupName = QString("%1.1").arg(m_logFilePath);
    QFile::remove(backupName);
    QFile::rename(m_logFilePath, backupName);
    
    // 重新打开日志文件
    if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
        if (m_logStream) {
            delete m_logStream;
        }
        m_logStream = new QTextStream(m_logFile);
        // Qt6默认使用UTF-8，不需要设置编码
    }
}
