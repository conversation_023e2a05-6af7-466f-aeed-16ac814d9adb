#ifndef LEGACY_API_ADAPTER_H
#define LEGACY_API_ADAPTER_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <functional>
#include "../network/engines/network_engine_interface.h"

// 前向声明
class OrderAPI;

/**
 * @brief 遗留API适配器
 * 
 * 这个适配器类用于在新架构和遗留OrderAPI之间建立桥梁，
 * 实现渐进式重构而不破坏现有功能。
 * 
 * 主要功能：
 * - 将遗留OrderAPI的接口适配到新的网络引擎架构
 * - 提供统一的API调用接口
 * - 支持逐步迁移到新架构
 */
class LegacyApiAdapter : public QObject
{
    Q_OBJECT

public:
    explicit LegacyApiAdapter(QObject *parent = nullptr);
    ~LegacyApiAdapter();

    /**
     * @brief 初始化适配器
     * @param useNewEngine 是否使用新的网络引擎架构
     * @return 初始化是否成功
     */
    bool initialize(bool useNewEngine = false);

    /**
     * @brief 设置网络引擎
     * @param engine 网络引擎实例
     */
    void setNetworkEngine(INetworkEngine* engine);

    /**
     * @brief 登录预检
     * @param account 账号
     * @param proxy 代理设置
     * @param callback 回调函数
     */
    void loginPreCheck(const QString& account, const QString& proxy, 
                      std::function<void(const QString&)> callback);

    /**
     * @brief 用户登录
     * @param account 账号
     * @param password 密码
     * @param proxy 代理设置
     * @param callback 回调函数
     */
    void login(const QString& account, const QString& password, 
              const QString& proxy, std::function<void(const QString&)> callback);

    /**
     * @brief 刷新订单列表
     * @param gameId 游戏ID
     * @param token 用户token
     * @param userId 用户ID
     * @param proxyHost 代理主机
     * @param proxyPort 代理端口
     * @param proxyType 代理类型
     * @param proxyUser 代理用户名
     * @param proxyPass 代理密码
     * @param priceStr 价格筛选
     * @param focusedFlag 关注标志
     */
    void refreshOrders(const QString &gameId, const QString &token, const QString &userId,
                      const QString &proxyHost, int proxyPort,
                      const QString &proxyType, const QString &proxyUser, const QString &proxyPass,
                      const QString &priceStr, int focusedFlag);

    /**
     * @brief 接受订单
     * @param orderId 订单ID
     * @param stamp 时间戳
     * @param token 用户token
     * @param userId 用户ID
     * @param payPassword 支付密码
     * @param loginId 登录ID
     * @param proxyHost 代理主机
     * @param proxyPort 代理端口
     * @param proxyUser 代理用户名
     * @param proxyPass 代理密码
     */
    void acceptOrder(const QString &orderId, const QString &stamp, const QString &token,
                    const QString &userId, const QString &payPassword, const QString &loginId,
                    const QString &proxyHost, int proxyPort,
                    const QString &proxyUser, const QString &proxyPass);

    /**
     * @brief 获取用户信息
     * @param token 用户token
     * @param userId 用户ID
     * @param proxyHost 代理主机
     * @param proxyPort 代理端口
     */
    void getUserInfo(const QString &token, const QString &userId,
                    const QString &proxyHost, int proxyPort);

    /**
     * @brief 设置页面大小
     * @param pageSize 页面大小
     */
    void setPageSize(int pageSize);

    /**
     * @brief 获取当前使用的引擎类型
     * @return 引擎类型字符串
     */
    QString getCurrentEngineType() const;

signals:
    // 适配后的信号，保持与UI层的兼容性
    void loginResult(bool success, const QString &message, const QString &token);
    void orderRefreshResult(bool success, const QString &message, const QJsonArray &orders);
    void orderAcceptResult(bool success, const QString &message, const QString &orderId);
    void userInfoResult(bool success, const QString &message, const QJsonObject &userInfo);
    void networkError(const QString &error);
    void debugLog(const QString &message);
    void fastOrderFound(const QString &orderInfo);

private slots:
    // 处理来自 OrderAPI 的信号（优化后的参数，只保留实际使用的）
    void onLegacyLoginResult(bool success, const QString& message, const QString& token);
    void onLegacyOrderRefreshResult(bool success, const QString& message, const QJsonArray& orders);
    void onLegacyOrderAcceptResult(bool success, const QString& message);
    void onLegacyUserInfoResult(bool success, const QString& message, const QJsonObject& userInfo);
    void onLegacyNetworkError(const QString& error);
    void onLegacyDebugLog(const QString& message);
    void onLegacyFastOrderFound(const QJsonObject& orderObj);

private:
    OrderAPI* m_legacyApi;           // 遗留API实例
    INetworkEngine* m_networkEngine; // 新网络引擎
    bool m_useNewEngine;             // 是否使用新引擎
    bool m_initialized;              // 初始化状态

    // 内部辅助方法
    void connectLegacySignals();
    void disconnectLegacySignals();
    QString buildPostData(const QList<QPair<QString, QString>> &params);
    QString signstr(const QString &action, const QString &data, const QString &token);
};

#endif // LEGACY_API_ADAPTER_H
