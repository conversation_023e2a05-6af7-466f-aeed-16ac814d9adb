#include "encryption_service.h"
#include "../utils/simple_logger.h"
#include <QCryptographicHash>
#include <QRandomGenerator>
#include <QDateTime>

EncryptionService::EncryptionService(QObject* parent)
    : QObject(parent)
{
    // 可以在这里设置默认的加密参数
}

QString EncryptionService::encryptPassword(const QString& password, const QString& loginId) const
{
    if (password.isEmpty()) {
        LOG_WARNING(LogCategory::SECURITY, "尝试加密空密码");
        return QString();
    }
    
    LOG_DEBUG(LogCategory::SECURITY, "开始密码加密");
    
    // 生成盐值
    QString salt = generateSalt(m_saltLength);
    
    // 组合密码、盐值和登录ID进行加密
    QString combined = combineForEncryption(password, salt, loginId);
    
    // 生成MD5哈希
    QString encrypted = md5Hash(combined);
    
    LOG_DEBUG(LogCategory::SECURITY, "密码加密完成");
    
    return encrypted;
}

QString EncryptionService::generateSignature(const QString& action, const QString& data, const QString& token) const
{
    LOG_DEBUG(LogCategory::SECURITY, QString("生成签名: action=%1").arg(action));
    
    // 组合签名数据
    QString signatureData = action + data + token + generateTimestamp();
    
    // 生成签名
    QString signature = md5Hash(signatureData);
    
    LOG_DEBUG(LogCategory::SECURITY, "签名生成完成");
    
    return signature;
}

QString EncryptionService::generateMD5Hash(const QString& input) const
{
    return md5Hash(input);
}

QString EncryptionService::generateRandomString(int length) const
{
    const QString chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    QString result;
    
    for (int i = 0; i < length; ++i) {
        int index = QRandomGenerator::global()->bounded(chars.length());
        result.append(chars.at(index));
    }
    
    return result;
}

QString EncryptionService::generateTimestamp() const
{
    return QString::number(QDateTime::currentSecsSinceEpoch());
}

QString EncryptionService::md5Hash(const QString& input) const
{
    QByteArray data = input.toUtf8();
    QByteArray hash = QCryptographicHash::hash(data, QCryptographicHash::Md5);
    return hash.toHex().toLower();
}

QString EncryptionService::generateSalt(int length) const
{
    return generateRandomString(length);
}

QString EncryptionService::combineForEncryption(const QString& password, const QString& salt, const QString& loginId) const
{
    // 根据具体的加密需求组合数据
    // 这里使用简单的组合方式，实际项目中可能需要更复杂的算法
    
    if (loginId.isEmpty()) {
        return password + salt;
    } else {
        return password + salt + loginId;
    }
}
