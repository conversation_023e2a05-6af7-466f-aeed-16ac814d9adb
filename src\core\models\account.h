#ifndef ACCOUNT_H
#define ACCOUNT_H

#include <QString>
#include <QDateTime>

class OrderAPI;

/**
 * @brief 新架构的账号信息数据模型 (避免与旧版本冲突)
 */
struct NewAccountInfo {
    QString username;           // 用户名
    QString password;           // 密码
    QString proxyHost;          // 代理主机
    int proxyPort = 0;          // 代理端口
    QString proxyType;          // 代理类型
    QString proxyUser;          // 代理用户名
    QString proxyPass;          // 代理密码
    QString token;              // 登录令牌
    QString userId;             // 用户ID
    bool isLoggedIn = false;    // 是否已登录
    QString uid;                // 用户UID
    bool loginSent = false;     // 是否已发送登录请求
    OrderAPI* api = nullptr;    // API对象指针
    QDateTime lastLoginTime;    // 最后登录时间
    
    // 构造函数
    NewAccountInfo() = default;
    NewAccountInfo(const QString& user, const QString& pass,
                   const QString& host = "", int port = 0,
                   const QString& type = "http",
                   const QString& proxyUser = "", const QString& proxyPass = "",
                   const QString& token = "", const QString& userId = "",
                   bool loggedIn = false, const QString& uid = "");
    
    // 工具方法
    bool isValid() const;
    void reset();
    QString toString() const;
};

/**
 * @brief 新架构的主账号信息 (避免与旧版本冲突)
 */
struct NewMainAccountInfo {
    QString username;
    QString password;
    QString token;
    QString userId;
    QString uid;
    bool isLoggedIn = false;
    QDateTime lastLoginTime;
    
    NewMainAccountInfo() = default;
    NewMainAccountInfo(const QString& user, const QString& pass,
                      const QString& token = "", const QString& userId = "",
                      const QString& uid = "", bool loggedIn = false);
    
    bool isValid() const;
    void reset();
};

#endif // ACCOUNT_H
