#ifndef BUSINESS_LOGIC_H
#define BUSINESS_LOGIC_H

#include <QObject>
#include <QString>
#include <QTimer>
#include <functional>
#include "../network/request_builder.h"
#include "../network/response_processor.h"
#include "encryption_service.h"

/**
 * @brief 业务逻辑模块 - 专门负责业务流程的编排和执行
 * 
 * 模块化设计：每个类负责一个特定的业务领域
 */

// ==================== 登录业务逻辑 ====================

class LoginBusinessLogic : public QObject
{
    Q_OBJECT

public:
    struct LoginConfig {
        int maxRetries;
        int retryDelayMs;
        bool autoRetry;
        bool rememberCredentials;

        // 构造函数设置默认值
        LoginConfig()
            : maxRetries(3)
            , retryDelayMs(1000)
            , autoRetry(true)
            , rememberCredentials(false)
        {}
    };

    explicit LoginBusinessLogic(QObject* parent = nullptr);

    // ==================== 核心业务方法 ====================
    
    /**
     * @brief 执行完整的登录流程
     */
    void executeLogin(const QString& username,
                     const QString& password,
                     const LoginConfig& config = LoginConfig());
    
    /**
     * @brief 验证登录凭据
     */
    bool validateCredentials(const QString& username, const QString& password) const;
    
    /**
     * @brief 检查登录状态
     */
    bool isLoggedIn() const { return !m_currentToken.isEmpty(); }
    
    /**
     * @brief 获取当前用户信息
     */
    ResponseProcessor::LoginResult getCurrentUser() const { return m_currentUser; }
    
    /**
     * @brief 登出
     */
    void logout();

signals:
    void loginStarted(const QString& username);
    void loginProgress(const QString& step, int percentage);
    void loginCompleted(bool success, const QString& message);
    void loginFailed(const QString& error, int retryCount);

private slots:
    void handleRetryTimer();

private:
    // NetworkClient已删除
    EncryptionService* m_encryptionService;
    RequestBuilder m_requestBuilder;
    ResponseProcessor m_responseProcessor;
    
    // 登录状态
    QString m_currentToken;
    ResponseProcessor::LoginResult m_currentUser;
    
    // 重试机制
    QTimer* m_retryTimer;
    int m_currentRetryCount = 0;
    LoginConfig m_currentConfig;
    QString m_pendingUsername;
    QString m_pendingPassword;
    
    // ==================== 内部业务方法 ====================
    
    /**
     * @brief 执行预检验步骤
     */
    void executePreCheck(const QString& username, const QString& encryptedPassword);
    
    /**
     * @brief 执行登录步骤
     */
    void executeLoginStep(const QString& username, const QString& encryptedPassword, const QString& loginId);
    
    /**
     * @brief 处理登录重试
     */
    void handleLoginRetry(const QString& error);
    
    /**
     * @brief 更新登录状态
     */
    void updateLoginState(const ResponseProcessor::LoginResult& result);
};

// ==================== 订单业务逻辑 ====================

class OrderBusinessLogic : public QObject
{
    Q_OBJECT

public:
    struct OrderConfig {
        int refreshIntervalMs;
        int maxOrdersPerRequest;
        bool autoRefresh;
        QString gameId;

        // 构造函数设置默认值
        OrderConfig()
            : refreshIntervalMs(5000)
            , maxOrdersPerRequest(50)
            , autoRefresh(true)
            , gameId("110")
        {}
    };

    explicit OrderBusinessLogic(QObject* parent = nullptr);
  

    // ==================== 核心业务方法 ====================
    
    /**
     * @brief 刷新订单列表
     */
    void refreshOrders(const QString& token,
                      const QString& userId,
                      const OrderConfig& config = OrderConfig());
    
    /**
     * @brief 接受订单
     */
    void acceptOrder(const QString& orderId,
                    const QString& token,
                    const QString& userId,
                    const QString& payPassword,
                    const QString& loginId);
    
    /**
     * @brief 过滤订单
     */
    QList<ResponseProcessor::OrderInfo> filterOrders(
        const QList<ResponseProcessor::OrderInfo>& orders,
        std::function<bool(const ResponseProcessor::OrderInfo&)> filter) const;
    
    /**
     * @brief 排序订单
     */
    QList<ResponseProcessor::OrderInfo> sortOrders(
        const QList<ResponseProcessor::OrderInfo>& orders,
        std::function<bool(const ResponseProcessor::OrderInfo&, const ResponseProcessor::OrderInfo&)> comparator) const;
    
    /**
     * @brief 获取当前订单列表
     */
    QList<ResponseProcessor::OrderInfo> getCurrentOrders() const { return m_currentOrders; }
    
    /**
     * @brief 开始自动刷新
     */
    void startAutoRefresh(const QString& token, const QString& userId, const OrderConfig& config = OrderConfig());
    
    /**
     * @brief 停止自动刷新
     */
    void stopAutoRefresh();

signals:
    void ordersRefreshStarted();
    void ordersRefreshed(const QList<ResponseProcessor::OrderInfo>& orders);
    void orderAcceptStarted(const QString& orderId);
    void orderAccepted(const QString& orderId, bool success, const QString& message);
    void orderFilterChanged(int totalCount, int filteredCount);

private slots:
    void handleAutoRefreshTimer();

private:
    // NetworkClient已删除
    RequestBuilder m_requestBuilder;
    ResponseProcessor m_responseProcessor;
    
    // 订单状态
    QList<ResponseProcessor::OrderInfo> m_currentOrders;
    
    // 自动刷新
    QTimer* m_autoRefreshTimer;
    QString m_autoRefreshToken;
    QString m_autoRefreshUserId;
    OrderConfig m_autoRefreshConfig;
    
    // ==================== 内部业务方法 ====================
    
    /**
     * @brief 处理订单列表响应
     */
    void handleOrderListResponse(const QString& response);
    
    /**
     * @brief 处理接单响应
     */
    void handleAcceptOrderResponse(const QString& orderId, const QString& response);
    
    /**
     * @brief 验证订单数据
     */
    bool validateOrderData(const ResponseProcessor::OrderInfo& order) const;
};

// ==================== 用户业务逻辑 ====================

class UserBusinessLogic : public QObject
{
    Q_OBJECT

public:
    explicit UserBusinessLogic(QObject* parent = nullptr);
  

    // ==================== 核心业务方法 ====================
    
    /**
     * @brief 获取用户信息
     */
    void fetchUserInfo(const QString& token, const QString& userId);
    
    /**
     * @brief 更新用户余额
     */
    void updateBalance(const QString& token, const QString& userId);
    
    /**
     * @brief 检查支付密码
     */
    void checkPayPassword(const QString& token, const QString& userId);
    
    /**
     * @brief 获取当前用户信息
     */
    ResponseProcessor::UserInfo getCurrentUserInfo() const { return m_currentUserInfo; }

signals:
    void userInfoUpdated(const ResponseProcessor::UserInfo& userInfo);
    void balanceUpdated(double balance);
    void payPasswordStatusChanged(bool hasPayPassword);

private:
    // NetworkClient已删除
    RequestBuilder m_requestBuilder;
    ResponseProcessor m_responseProcessor;
    
    // 用户状态
    ResponseProcessor::UserInfo m_currentUserInfo;
    
    // ==================== 内部业务方法 ====================
    
    /**
     * @brief 处理用户信息响应
     */
    void handleUserInfoResponse(const QString& response);
};

// ==================== 业务逻辑工厂 ====================

class BusinessLogicFactory
{
public:
    /**
     * @brief 创建登录业务逻辑
     */
    static LoginBusinessLogic* createLoginLogic(QObject* parent = nullptr);
    
    /**
     * @brief 创建订单业务逻辑
     */
    static OrderBusinessLogic* createOrderLogic(QObject* parent = nullptr);
    
    /**
     * @brief 创建用户业务逻辑
     */
    static UserBusinessLogic* createUserLogic(QObject* parent = nullptr);
};

#endif // BUSINESS_LOGIC_H
