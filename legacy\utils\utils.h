#ifndef UTILS_H
#define UTILS_H

#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <QDateTime>
#include <QFileInfo>
#include <QCryptographicHash>
#include <functional>
#include <algorithm>
#include <chrono>

/**
 * @brief 通用工具函数 - 消除代码重复，提供简洁的工具方法
 */
namespace Utils {

    // ==================== 字符串工具 ====================

    inline bool isEmpty(const QString& str) {
        return str.trimmed().isEmpty();
    }

    inline QString truncate(const QString& str, int maxLen = 100) {
        return str.length() > maxLen ? str.left(maxLen) + "..." : str;
    }

    // ==================== 条件执行工具 ====================

    template<typename T>
    inline T valueOrDefault(const T& value,
                           const T& defaultValue,
                           std::function<bool(const T&)> isValid) {
        return isValid(value) ? value : defaultValue;
    }

    // ==================== JSON工具 ====================

    inline QString safeJsonString(const QJsonObject& obj,
                                 const QString& key,
                                 const QString& defaultValue = "") {
        return obj.contains(key) && obj[key].isString()
            ? obj[key].toString()
            : defaultValue;
    }

    inline int safeJsonInt(const QJsonObject& obj,
                          const QString& key,
                          int defaultValue = 0) {
        if (!obj.contains(key)) return defaultValue;

        const auto& value = obj[key];
        return value.isDouble() ? value.toInt()
             : value.isString() ? value.toString().toInt()
             : defaultValue;
    }
    
    // 集合工具
    template<typename Container, typename Predicate>
    inline bool any_of(const Container& container, Predicate pred) {
        return std::any_of(container.begin(), container.end(), pred);
    }
    
    template<typename Container, typename Predicate>
    inline auto find_if(const Container& container, Predicate pred) {
        return std::find_if(container.begin(), container.end(), pred);
    }
    
    // 错误处理工具
    struct Result {
        bool success = false;
        QString message;
        
        static Result ok(const QString& msg = "成功") { return {true, msg}; }
        static Result error(const QString& msg) { return {false, msg}; }
        
        bool isOk() const { return success; }
        bool isError() const { return !success; }
    };
    
    // 网络工具
    inline QString buildUrl(const QString& base, const QString& action) {
        return base + "?Action=" + action;
    }
    
    inline QString buildPostData(const std::initializer_list<std::pair<QString, QString>>& params) {
        QStringList parts;
        for (const auto& [key, value] : params) {
            parts << key + "=" + value;
        }
        return parts.join("&");
    }
    
    // 时间工具
    inline QString currentTimestamp() {
        return QString::number(QDateTime::currentSecsSinceEpoch());
    }
    
    // 日志工具
    inline QString formatLogMessage(const QString& category, const QString& message) {
        return QString("[%1] %2").arg(category, message);
    }
    
    // 验证工具
    inline bool isValidEmail(const QString& email) {
        return email.contains("@") && email.contains(".");
    }
    
    inline bool isValidPhone(const QString& phone) {
        return phone.length() >= 10 && phone.length() <= 15;
    }
    
    // 加密工具
    inline QString md5(const QString& input) {
        return QCryptographicHash::hash(input.toUtf8(), QCryptographicHash::Md5).toHex().toLower();
    }
    
    // 文件工具
    inline bool fileExists(const QString& path) {
        return QFileInfo(path).exists();
    }
    
    inline bool isExecutable(const QString& path) {
        return QFileInfo(path).isExecutable();
    }
    
    // 性能工具
    class Timer {
    public:
        Timer() : start(std::chrono::high_resolution_clock::now()) {}
        
        qint64 elapsed() const {
            return std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::high_resolution_clock::now() - start).count();
        }
        
    private:
        std::chrono::high_resolution_clock::time_point start;
    };
    
    // 函数式编程工具
    template<typename T, typename Func>
    inline auto map(const QList<T>& list, Func func) -> QList<decltype(func(T{}))> {
        QList<decltype(func(T{}))> result;
        std::transform(list.begin(), list.end(), std::back_inserter(result), func);
        return result;
    }
    
    template<typename T, typename Predicate>
    inline QList<T> filter(const QList<T>& list, Predicate pred) {
        QList<T> result;
        std::copy_if(list.begin(), list.end(), std::back_inserter(result), pred);
        return result;
    }
    
    // 安全转换工具
    template<typename T>
    inline T safeCast(const QVariant& variant, const T& defaultValue = T{}) {
        return variant.canConvert<T>() ? variant.value<T>() : defaultValue;
    }
}

// 便利宏
#define RETURN_IF_EMPTY(str, msg) if (Utils::isEmpty(str)) return Utils::Result::error(msg)
#define RETURN_IF_NULL(ptr, msg) if (!ptr) return Utils::Result::error(msg)
#define LOG_AND_RETURN_ERROR(msg) do { LOG_ERROR(LogCategory::SYSTEM, msg); return Utils::Result::error(msg); } while(0)

#endif // UTILS_H
