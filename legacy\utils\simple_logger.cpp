#include "simple_logger.h"

SimpleLogger* SimpleLogger::s_instance = nullptr;

SimpleLogger* SimpleLogger::instance()
{
    if (!s_instance) {
        s_instance = new SimpleLogger();
    }
    return s_instance;
}

SimpleLogger::SimpleLogger(QObject* parent)
    : QObject(parent)
{
}

void SimpleLogger::debug(const QString& category, const QString& message)
{
    log(DEBUG, category, message);
}

void SimpleLogger::info(const QString& category, const QString& message)
{
    log(INFO, category, message);
}

void SimpleLogger::warning(const QString& category, const QString& message)
{
    log(WARNING, category, message);
}

void SimpleLogger::error(const QString& category, const QString& message)
{
    log(LOG_ERROR, category, message);
}

void SimpleLogger::log(Level level, const QString& category, const QString& message)
{
    if (level < m_level) {
        return;
    }
    
    QString formattedMessage = formatMessage(level, category, message);
    
    // 输出到控制台
    qDebug() << formattedMessage;
    
    // 发送信号（用于UI显示）
    emit logMessage(formattedMessage);
}

QString SimpleLogger::formatMessage(Level level, const QString& category, const QString& message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    QString levelStr = levelToString(level);
    
    return QString("[%1] [%2] [%3] %4")
           .arg(timestamp)
           .arg(levelStr)
           .arg(category)
           .arg(message);
}

QString SimpleLogger::levelToString(Level level)
{
    switch (level) {
        case DEBUG: return "DEBUG";
        case INFO: return "INFO";
        case WARNING: return "WARN";
        case LOG_ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}
