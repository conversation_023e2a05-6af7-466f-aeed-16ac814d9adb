# Qt generated files
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qmlc
*.jsc
Makefile*
*build-*
*.qm
.qmake.cache
.qmake.stash

# Qt Creator
*.autosave

# Qt Creator Qml
*.qmlproject.user
*.qmlproject.user.*

# Qt Creator CMake
CMakeLists.txt.user*

# Build directories
build/
build-*/
debug/
release/
Debug/
Release/

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/

# Visual Studio
.vs/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates

# MinGW
*.exe
*.dll
*.a
*.o
*.obj

# Temporary files
*.tmp
*.temp
*~
*.swp
*.swo
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.sublime-*

# Log files
*.log
logs/

# Configuration files (if they contain sensitive data)
config.ini
settings.ini

# Backup files
*.bak
*.backup

# Test results
test_results/
coverage/

# Dependencies
vcpkg_installed/
packages/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
