#include "order.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QDebug>

// NewOrderInfo 实现
NewOrderInfo::NewOrderInfo(const QString& serialNo, const QString& title, double price)
    : serialNo(serialNo)
    , title(title)
    , price(price)
    , createTime(QDateTime::currentDateTime())
    , updateTime(QDateTime::currentDateTime())
{
}

NewOrderInfo NewOrderInfo::fromJson(const QJsonObject& json)
{
    NewOrderInfo order;
    order.serialNo = json["SerialNo"].toString();
    order.title = json["Title"].toString();
    order.price = json["Price"].toDouble();
    order.ensure1 = json["Ensure1"].toDouble();
    order.ensure2 = json["Ensure2"].toDouble();
    order.ensure3 = json["Ensure3"].toDouble();
    order.gameId = json["GameID"].toString();
    order.zoneServerId = json["ZoneServerID"].toString();
    order.userId = json["UserID"].toString();
    order.isPub = json["IsPub"].toInt();
    
    // 设置时间戳
    order.createTime = QDateTime::currentDateTime();
    order.updateTime = order.createTime;
    
    return order;
}

QJsonObject OrderInfo::toJson() const
{
    QJsonObject json;
    json["SerialNo"] = serialNo;
    json["Title"] = title;
    json["Price"] = price;
    json["Ensure1"] = ensure1;
    json["Ensure2"] = ensure2;
    json["Ensure3"] = ensure3;
    json["GameID"] = gameId;
    json["ZoneServerID"] = zoneServerId;
    json["UserID"] = userId;
    json["IsPub"] = isPub;
    json["CreateTime"] = createTime.toString(Qt::ISODate);
    json["UpdateTime"] = updateTime.toString(Qt::ISODate);
    return json;
}

bool NewOrderInfo::isValid() const
{
    return !serialNo.isEmpty() && !title.isEmpty() && price >= 0;
}

bool NewOrderInfo::isExpired() const
{
    if (!expireTime.isValid()) {
        return false;
    }
    return QDateTime::currentDateTime() > expireTime;
}

bool NewOrderInfo::matchesKeywords(const QStringList& includeKeywords,
                                   const QStringList& excludeKeywords) const
{
    // 检查包含关键词
    if (!includeKeywords.isEmpty()) {
        bool hasInclude = false;
        for (const QString& keyword : includeKeywords) {
            if (title.contains(keyword, Qt::CaseInsensitive) ||
                description.contains(keyword, Qt::CaseInsensitive)) {
                hasInclude = true;
                break;
            }
        }
        if (!hasInclude) {
            return false;
        }
    }
    
    // 检查排除关键词
    for (const QString& keyword : excludeKeywords) {
        if (title.contains(keyword, Qt::CaseInsensitive) ||
            description.contains(keyword, Qt::CaseInsensitive)) {
            return false;
        }
    }
    
    return true;
}

bool OrderInfo::matchesPriceRange(double minPrice, double maxPrice) const
{
    return price >= minPrice && price <= maxPrice;
}

QString OrderInfo::getStatusString() const
{
    switch (status) {
    case OrderStatus::AVAILABLE: return "可接单";
    case OrderStatus::ACCEPTED: return "已接单";
    case OrderStatus::IN_PROGRESS: return "进行中";
    case OrderStatus::COMPLETED: return "已完成";
    case OrderStatus::CANCELLED: return "已取消";
    case OrderStatus::EXPIRED: return "已过期";
    default: return "未知";
    }
}

QString OrderInfo::getPriorityString() const
{
    switch (priority) {
    case OrderPriority::LOW: return "低";
    case OrderPriority::NORMAL: return "普通";
    case OrderPriority::HIGH: return "高";
    case OrderPriority::URGENT: return "紧急";
    default: return "未知";
    }
}

QString OrderInfo::toString() const
{
    return QString("OrderInfo{serialNo=%1, title=%2, price=%3, status=%4}")
           .arg(serialNo)
           .arg(title)
           .arg(price)
           .arg(getStatusString());
}

bool OrderInfo::operator==(const OrderInfo& other) const
{
    return serialNo == other.serialNo;
}

bool OrderInfo::operator!=(const OrderInfo& other) const
{
    return !(*this == other);
}

// OrderFilter 实现
bool OrderFilter::matches(const OrderInfo& order) const
{
    // 价格范围检查
    if (!order.matchesPriceRange(minPrice, maxPrice)) {
        return false;
    }
    
    // 关键词检查
    if (!order.matchesKeywords(includeKeywords, excludeKeywords)) {
        return false;
    }
    
    // 游戏ID检查
    if (!gameIds.isEmpty() && !gameIds.contains(order.gameId)) {
        return false;
    }
    
    // 区服ID检查
    if (!zoneServerIds.isEmpty() && !zoneServerIds.contains(order.zoneServerId)) {
        return false;
    }
    
    // 状态检查
    if (order.status < minStatus || order.status > maxStatus) {
        return false;
    }
    
    // 公开性检查
    if (onlyPublic && order.isPub != 1) {
        return false;
    }
    
    return true;
}

QList<OrderInfo> OrderFilter::filter(const QList<OrderInfo>& orders) const
{
    QList<OrderInfo> result;
    for (const OrderInfo& order : orders) {
        if (matches(order)) {
            result.append(order);
        }
    }
    return result;
}

QJsonObject OrderFilter::toJson() const
{
    QJsonObject json;
    json["includeKeywords"] = QJsonArray::fromStringList(includeKeywords);
    json["excludeKeywords"] = QJsonArray::fromStringList(excludeKeywords);
    json["minPrice"] = minPrice;
    json["maxPrice"] = maxPrice;
    json["gameIds"] = QJsonArray::fromStringList(gameIds);
    json["zoneServerIds"] = QJsonArray::fromStringList(zoneServerIds);
    json["onlyPublic"] = onlyPublic;
    return json;
}

OrderFilter OrderFilter::fromJson(const QJsonObject& json)
{
    OrderFilter filter;
    
    // 转换关键词数组
    QJsonArray includeArray = json["includeKeywords"].toArray();
    for (const QJsonValue& value : includeArray) {
        filter.includeKeywords.append(value.toString());
    }
    
    QJsonArray excludeArray = json["excludeKeywords"].toArray();
    for (const QJsonValue& value : excludeArray) {
        filter.excludeKeywords.append(value.toString());
    }
    
    filter.minPrice = json["minPrice"].toDouble();
    filter.maxPrice = json["maxPrice"].toDouble();
    filter.onlyPublic = json["onlyPublic"].toBool();
    
    return filter;
}

// OrderStats 实现
void OrderStats::reset()
{
    totalOrders = 0;
    availableOrders = 0;
    acceptedOrders = 0;
    completedOrders = 0;
    totalValue = 0.0;
    averagePrice = 0.0;
    minPrice = 0.0;
    maxPrice = 0.0;
    lastUpdateTime = QDateTime::currentDateTime();
}

void OrderStats::update(const QList<OrderInfo>& orders)
{
    reset();
    
    if (orders.isEmpty()) {
        return;
    }
    
    totalOrders = orders.size();
    minPrice = orders.first().price;
    maxPrice = orders.first().price;
    
    for (const OrderInfo& order : orders) {
        totalValue += order.price;
        
        if (order.price < minPrice) minPrice = order.price;
        if (order.price > maxPrice) maxPrice = order.price;
        
        switch (order.status) {
        case OrderStatus::AVAILABLE:
            availableOrders++;
            break;
        case OrderStatus::ACCEPTED:
            acceptedOrders++;
            break;
        case OrderStatus::COMPLETED:
            completedOrders++;
            break;
        default:
            break;
        }
    }
    
    averagePrice = totalValue / totalOrders;
    lastUpdateTime = QDateTime::currentDateTime();
}

QString OrderStats::toString() const
{
    return QString("OrderStats{total=%1, available=%2, avgPrice=%.2f}")
           .arg(totalOrders)
           .arg(availableOrders)
           .arg(averagePrice);
}
