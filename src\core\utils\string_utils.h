#ifndef STRING_UTILS_H
#define STRING_UTILS_H

#include <QString>
#include <QStringList>
#include <QRegularExpression>
#include <QTextCodec>

/**
 * @brief 字符串处理工具类
 */
class StringUtils {
public:
    // 字符串清理
    static QString trim(const QString& str);
    static QString trimLeft(const QString& str);
    static QString trimRight(const QString& str);
    static QString removeWhitespace(const QString& str);
    static QString normalizeWhitespace(const QString& str); // 将多个空白字符替换为单个空格
    
    // 大小写转换
    static QString toCamelCase(const QString& str);
    static QString toPascalCase(const QString& str);
    static QString toSnakeCase(const QString& str);
    static QString toKebabCase(const QString& str);
    
    // 字符串验证
    static bool isEmpty(const QString& str);
    static bool isBlank(const QString& str); // 空或只包含空白字符
    static bool isNumeric(const QString& str);
    static bool isAlpha(const QString& str);
    static bool isAlphaNumeric(const QString& str);
    static bool isEmail(const QString& str);
    static bool isUrl(const QString& str);
    static bool isPhoneNumber(const QString& str);
    
    // 字符串比较
    static bool equalsIgnoreCase(const QString& str1, const QString& str2);
    static bool containsIgnoreCase(const QString& str, const QString& substring);
    static bool startsWithIgnoreCase(const QString& str, const QString& prefix);
    static bool endsWithIgnoreCase(const QString& str, const QString& suffix);
    
    // 字符串搜索和替换
    static int indexOfIgnoreCase(const QString& str, const QString& substring, int from = 0);
    static QString replaceIgnoreCase(const QString& str, const QString& before, const QString& after);
    static QString replaceAll(const QString& str, const QStringList& patterns, const QString& replacement);
    
    // 字符串分割和连接
    static QStringList split(const QString& str, const QString& separator, bool skipEmpty = true);
    static QStringList splitByRegex(const QString& str, const QRegularExpression& regex);
    static QString join(const QStringList& list, const QString& separator);
    static QString joinWithAnd(const QStringList& list, const QString& separator = ", ", const QString& lastSeparator = " 和 ");
    
    // 字符串截取
    static QString left(const QString& str, int length, const QString& ellipsis = "...");
    static QString right(const QString& str, int length, const QString& ellipsis = "...");
    static QString mid(const QString& str, int start, int length, const QString& ellipsis = "...");
    static QString truncate(const QString& str, int maxLength, const QString& ellipsis = "...");
    
    // 字符串填充
    static QString padLeft(const QString& str, int width, QChar fillChar = ' ');
    static QString padRight(const QString& str, int width, QChar fillChar = ' ');
    static QString padCenter(const QString& str, int width, QChar fillChar = ' ');
    
    // 字符串重复
    static QString repeat(const QString& str, int count);
    static QString repeat(QChar ch, int count);
    
    // 编码转换
    static QString toUtf8(const QString& str);
    static QString fromUtf8(const QByteArray& bytes);
    static QString toBase64(const QString& str);
    static QString fromBase64(const QString& base64Str);
    static QString urlEncode(const QString& str);
    static QString urlDecode(const QString& str);
    static QString htmlEscape(const QString& str);
    static QString htmlUnescape(const QString& str);
    
    // 随机字符串生成
    static QString randomString(int length, bool includeNumbers = true, bool includeSymbols = false);
    static QString randomAlphaString(int length);
    static QString randomNumericString(int length);
    static QString generateUuid();
    
    // 字符串格式化
    static QString formatBytes(qint64 bytes, int precision = 2);
    static QString formatDuration(qint64 milliseconds);
    static QString formatNumber(double number, int decimals = 2, const QString& thousandsSeparator = ",");
    static QString formatPercentage(double value, int decimals = 1);
    
    // 字符串匹配
    static bool matchesPattern(const QString& str, const QString& pattern); // 支持 * 和 ? 通配符
    static bool matchesRegex(const QString& str, const QRegularExpression& regex);
    static QStringList extractMatches(const QString& str, const QRegularExpression& regex);
    
    // 字符串距离计算
    static int levenshteinDistance(const QString& str1, const QString& str2);
    static double similarity(const QString& str1, const QString& str2); // 0.0-1.0
    
    // 中文处理
    static bool containsChinese(const QString& str);
    static QString extractChinese(const QString& str);
    static QString removeChinese(const QString& str);
    static int chineseLength(const QString& str); // 中文字符按2个字符计算
    
    // 敏感信息处理
    static QString maskEmail(const QString& email);
    static QString maskPhone(const QString& phone);
    static QString maskCreditCard(const QString& cardNumber);
    static QString maskString(const QString& str, int visibleStart = 3, int visibleEnd = 3, QChar maskChar = '*');

private:
    StringUtils() = delete; // 工具类，不允许实例化
    
    static QRegularExpression s_emailRegex;
    static QRegularExpression s_urlRegex;
    static QRegularExpression s_phoneRegex;
    static QRegularExpression s_chineseRegex;
    
    static void initializeRegexes();
};

// 便捷宏定义
#define STR_EMPTY(str) StringUtils::isEmpty(str)
#define STR_BLANK(str) StringUtils::isBlank(str)
#define STR_EQUALS_IC(str1, str2) StringUtils::equalsIgnoreCase(str1, str2)
#define STR_CONTAINS_IC(str, sub) StringUtils::containsIgnoreCase(str, sub)
#define STR_TRUNCATE(str, len) StringUtils::truncate(str, len)

#endif // STRING_UTILS_H
