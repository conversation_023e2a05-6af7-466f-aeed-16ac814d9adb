#ifndef RESPONSE_PROCESSOR_H
#define RESPONSE_PROCESSOR_H

#include <QString>
#include <QJsonObject>
#include <QJsonArray>
#include <functional>

/**
 * @brief 响应处理器 - 专门负责处理各种类型的HTTP响应
 * 
 * 函数化设计：每个函数负责处理特定类型的响应
 */
class ResponseProcessor
{
public:
    // ==================== 数据结构 ====================
    
    struct ProcessResult {
        bool success = false;
        QString message;
        QJsonObject data;
        int errorCode = 0;
        
        bool isSuccess() const { return success; }
        bool hasData() const { return success && !data.isEmpty(); }
        QString getErrorMessage() const { return success ? "" : message; }
    };
    
    struct LoginResult {
        bool success = false;
        QString message;
        QString userId;
        QString token;
        QString loginId;
        QString nickname;
        double balance = 0.0;
        bool hasPayPassword = false;
        
        bool isValid() const { return success && !token.isEmpty(); }
    };
    
    struct OrderInfo {
        QString serialNo;
        QString title;
        double price = 0.0;
        QString publisherUserId;
        QString gameId;
        QString zoneServerId;
        bool isPublic = true;
        QString status;
        QString createTime;
        
        bool isValid() const { return !serialNo.isEmpty() && price > 0; }
    };
    
    struct UserInfo {
        QString userId;
        QString nickname;
        QString mobile;
        double balance = 0.0;
        bool hasPayPassword = false;
        QString level;
        int orderCount = 0;
        
        bool isValid() const { return !userId.isEmpty(); }
    };

    // ==================== 构造函数 ====================
    
    explicit ResponseProcessor() = default;
    
    // ==================== 登录响应处理 ====================
    
    /**
     * @brief 处理预检验响应
     */
    ProcessResult processPreCheckResponse(const QString& response) const;
    
    /**
     * @brief 处理登录响应
     */
    LoginResult processLoginResponse(const QString& response) const;
    
    // ==================== 订单响应处理 ====================
    
    /**
     * @brief 处理订单列表响应
     */
    QList<OrderInfo> processOrderListResponse(const QString& response) const;
    
    /**
     * @brief 处理接单响应
     */
    ProcessResult processAcceptOrderResponse(const QString& response) const;
    
    // ==================== 用户响应处理 ====================
    
    /**
     * @brief 处理用户信息响应
     */
    UserInfo processUserInfoResponse(const QString& response) const;
    
    /**
     * @brief 处理余额查询响应
     */
    double processBalanceResponse(const QString& response) const;
    
    // ==================== 通用响应处理 ====================
    
    /**
     * @brief 处理通用API响应
     */
    ProcessResult processGenericResponse(const QString& response) const;
    
    /**
     * @brief 验证响应格式
     */
    bool validateResponse(const QString& response) const;
    
    /**
     * @brief 提取错误信息
     */
    QString extractErrorMessage(const QJsonObject& obj) const;
    
    /**
     * @brief 检查响应是否成功
     */
    bool isResponseSuccessful(const QJsonObject& obj) const;

private:
    // ==================== 内部辅助函数 ====================
    
    /**
     * @brief 解析JSON响应
     */
    QJsonObject parseJsonResponse(const QString& response) const;
    
    /**
     * @brief 提取字符串字段
     */
    QString extractString(const QJsonObject& obj, const QString& key, const QString& defaultValue = "") const;
    
    /**
     * @brief 提取整数字段
     */
    int extractInt(const QJsonObject& obj, const QString& key, int defaultValue = 0) const;
    
    /**
     * @brief 提取浮点数字段
     */
    double extractDouble(const QJsonObject& obj, const QString& key, double defaultValue = 0.0) const;
    
    /**
     * @brief 提取布尔字段
     */
    bool extractBool(const QJsonObject& obj, const QString& key, bool defaultValue = false) const;
    
    /**
     * @brief 处理单个订单信息
     */
    OrderInfo processOrderInfo(const QJsonObject& orderObj) const;
    
    /**
     * @brief 处理用户基本信息
     */
    UserInfo processUserBasicInfo(const QJsonObject& userObj) const;
    
    /**
     * @brief 格式化错误消息
     */
    QString formatErrorMessage(const QString& operation, const QString& error, int code = 0) const;
};

// ==================== 便利函数 ====================

/**
 * @brief 创建默认响应处理器
 */
ResponseProcessor createDefaultResponseProcessor();

/**
 * @brief 处理响应的函数式接口
 */
template<typename T>
using ResponseHandler = std::function<T(const QString&)>;

/**
 * @brief 创建登录响应处理器
 */
ResponseHandler<ResponseProcessor::LoginResult> createLoginResponseHandler();

/**
 * @brief 创建订单列表响应处理器
 */
ResponseHandler<QList<ResponseProcessor::OrderInfo>> createOrderListResponseHandler();

/**
 * @brief 创建用户信息响应处理器
 */
ResponseHandler<ResponseProcessor::UserInfo> createUserInfoResponseHandler();

// ==================== 模板实现 ====================

template<typename T>
inline T processResponseWithHandler(const QString& response, ResponseHandler<T> handler)
{
    return handler(response);
}

#endif // RESPONSE_PROCESSOR_H
