#include "network_manager.h"
#include "engines/network_engine_interface.h"
#include "../core/utils/logger.h"
#include <QTimer>
#include <QRandomGenerator>

NetworkManager::NetworkManager(QObject* parent)
    : QObject(parent)
{
    NEW_LOG_INFO(NewLogCategory::NETWORK, "NetworkManager initialized with smart pointer management");
}

NetworkManager::~NetworkManager()
{
    // 智能指针自动清理，无需手动delete
    NEW_LOG_INFO(NewLogCategory::NETWORK, "NetworkManager destroyed");
}

bool NetworkManager::addEngine(const QString& name, std::unique_ptr<INetworkEngine> engine)
{
    if (!engine) {
        NEW_LOG_ERROR(NewLogCategory::NETWORK, QString("Cannot add null engine: %1").arg(name));
        return false;
    }
    
    QMutexLocker locker(&m_mutex);
    
    if (m_engines.contains(name)) {
        NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("Engine already exists, replacing: %1").arg(name));
    }
    
    EngineInfo info;
    info.engine = std::move(engine);  // 转移所有权
    info.stats.reset();
    info.isHealthy = true;
    info.lastHealthCheck = QDateTime::currentDateTime();
    
    m_engines[name] = std::move(info);
    
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("Engine added: %1").arg(name));
    return true;
}

bool NetworkManager::removeEngine(const QString& name)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_engines.contains(name)) {
        NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("Engine not found: %1").arg(name));
        return false;
    }
    
    // 智能指针自动清理
    m_engines.remove(name);
    
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("Engine removed: %1").arg(name));
    return true;
}

INetworkEngine* NetworkManager::getEngine(const QString& name) const
{
    QMutexLocker locker(&m_mutex);
    
    auto it = m_engines.find(name);
    if (it != m_engines.end()) {
        return it.value().engine.get();  // 返回原始指针
    }
    
    return nullptr;
}

QStringList NetworkManager::getAvailableEngines() const
{
    QMutexLocker locker(&m_mutex);
    return m_engines.keys();
}

bool NetworkManager::setPrimaryEngine(const QString& name)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_engines.contains(name)) {
        NEW_LOG_ERROR(NewLogCategory::NETWORK, QString("Engine not found: %1").arg(name));
        return false;
    }
    
    m_primaryEngine = name;
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("Primary engine set to: %1").arg(name));
    
    return true;
}

QString NetworkManager::getPrimaryEngine() const
{
    QMutexLocker locker(&m_mutex);
    return m_primaryEngine;
}

bool NetworkManager::setFallbackEngine(const QString& name)
{
    QMutexLocker locker(&m_mutex);
    
    if (!name.isEmpty() && !m_engines.contains(name)) {
        NEW_LOG_ERROR(NewLogCategory::NETWORK, QString("Engine not found: %1").arg(name));
        return false;
    }
    
    m_fallbackEngine = name;
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("Fallback engine set to: %1").arg(name));
    
    return true;
}

QString NetworkManager::getFallbackEngine() const
{
    QMutexLocker locker(&m_mutex);
    return m_fallbackEngine;
}

NetworkResult NetworkManager::executeRequest(const QString& url, 
                                            const QString& postData,
                                            const QJsonObject& headers,
                                            const QString& engineName)
{
    QString selectedEngine = engineName;
    
    // 如果没有指定引擎，选择最佳引擎
    if (selectedEngine.isEmpty()) {
        selectedEngine = selectBestEngine();
    }
    
    if (selectedEngine.isEmpty()) {
        NEW_LOG_ERROR(NewLogCategory::NETWORK, "No available engines");
        return NetworkResult(false, "", "No available engines");
    }
    
    QMutexLocker locker(&m_mutex);
    
    auto it = m_engines.find(selectedEngine);
    if (it == m_engines.end() || !it.value().engine) {
        NEW_LOG_ERROR(NewLogCategory::NETWORK, QString("Engine not available: %1").arg(selectedEngine));
        return NetworkResult(false, "", "Engine not available");
    }
    
    // 记录请求开始时间
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // 执行请求
    NetworkResult result = it.value().engine->executeRequest(url, postData, headers);
    
    // 计算响应时间
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    result.responseTime = duration.count();
    
    // 更新统计信息
    updateStats(selectedEngine, result);
    
    NEW_LOG_INFO(NewLogCategory::NETWORK, 
                 QString("Request completed: engine=%1, success=%2, time=%3ms")
                 .arg(selectedEngine)
                 .arg(result.success ? "true" : "false")
                 .arg(result.responseTime));
    
    return result;
}

void NetworkManager::executeRequestAsync(const QString& url,
                                        const QString& postData,
                                        const QJsonObject& headers,
                                        const QString& engineName)
{
    QString selectedEngine = engineName;
    
    // 如果没有指定引擎，选择最佳引擎
    if (selectedEngine.isEmpty()) {
        selectedEngine = selectBestEngine();
    }
    
    if (selectedEngine.isEmpty()) {
        emit requestFailed("", "No available engines");
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    auto it = m_engines.find(selectedEngine);
    if (it == m_engines.end() || !it.value().engine) {
        emit requestFailed(selectedEngine, "Engine not available");
        return;
    }
    
    // 异步执行请求（这里简化实现，实际应该在单独线程中执行）
    NetworkResult result = it.value().engine->executeRequest(url, postData, headers);
    
    // 更新统计信息
    updateStats(selectedEngine, result);
    
    // 发送结果信号
    if (result.success) {
        emit requestCompleted(selectedEngine, result);
    } else {
        emit requestFailed(selectedEngine, result.errorMessage);
    }
}

NetworkPerformanceStats NetworkManager::getStats(const QString& engineName) const
{
    QMutexLocker locker(&m_mutex);
    
    if (engineName.isEmpty()) {
        // 返回所有引擎的汇总统计
        NetworkPerformanceStats totalStats;
        totalStats.startTime = QDateTime::currentDateTime();
        
        for (auto it = m_engines.begin(); it != m_engines.end(); ++it) {
            const auto& stats = it.value().stats;
            totalStats.totalRequests += stats.totalRequests;
            totalStats.successfulRequests += stats.successfulRequests;
            totalStats.failedRequests += stats.failedRequests;
            totalStats.totalResponseTime += stats.totalResponseTime;
            
            if (stats.minResponseTime < totalStats.minResponseTime) {
                totalStats.minResponseTime = stats.minResponseTime;
            }
            if (stats.maxResponseTime > totalStats.maxResponseTime) {
                totalStats.maxResponseTime = stats.maxResponseTime;
            }
            
            if (stats.startTime < totalStats.startTime) {
                totalStats.startTime = stats.startTime;
            }
        }
        
        return totalStats;
    } else {
        auto it = m_engines.find(engineName);
        return (it != m_engines.end()) ? it.value().stats : NetworkPerformanceStats();
    }
}

QHash<QString, NetworkPerformanceStats> NetworkManager::getAllStats() const
{
    QMutexLocker locker(&m_mutex);
    
    QHash<QString, NetworkPerformanceStats> result;
    for (auto it = m_engines.begin(); it != m_engines.end(); ++it) {
        result[it.key()] = it.value().stats;
    }
    
    return result;
}

void NetworkManager::resetStats(const QString& engineName)
{
    QMutexLocker locker(&m_mutex);
    
    if (engineName.isEmpty()) {
        // 重置所有引擎的统计
        for (auto it = m_engines.begin(); it != m_engines.end(); ++it) {
            it.value().stats.reset();
        }
        NEW_LOG_INFO(NewLogCategory::NETWORK, "All engine stats reset");
    } else {
        auto it = m_engines.find(engineName);
        if (it != m_engines.end()) {
            it.value().stats.reset();
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("Engine stats reset: %1").arg(engineName));
        }
    }
}
