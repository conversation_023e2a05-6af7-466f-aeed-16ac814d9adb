#ifndef AUTHENTICATION_SERVICE_H
#define AUTHENTICATION_SERVICE_H

#include <QObject>
#include <QString>
#include <functional>

// 前向声明
class NetworkClient;
class EncryptionService;
class JsonParser;

/**
 * @brief 认证服务 - 专门负责用户认证
 * 
 * 单一责任：只负责用户登录、认证相关操作
 */
class AuthenticationService : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 认证结果
     */
    struct AuthResult {
        bool success = false;
        QString message;
        QString userId;
        QString token;
        QString loginId;
        
        bool isSuccess() const { return success && !token.isEmpty(); }
    };

    explicit AuthenticationService(QObject* parent = nullptr);
    
    // 依赖注入
    void setNetworkClient(NetworkClient* client) { m_networkClient = client; }
    void setEncryptionService(EncryptionService* service) { m_encryptionService = service; }
    void setJsonParser(JsonParser* parser) { m_jsonParser = parser; }
    
    // 核心职责：用户认证
    void loginAsync(const QString& username, const QString& password, 
                   std::function<void(AuthResult)> callback);
    
    AuthResult loginSync(const QString& username, const QString& password);
    
    // 认证状态管理
    bool isAuthenticated() const { return !m_currentToken.isEmpty(); }
    QString getCurrentToken() const { return m_currentToken; }
    QString getCurrentUserId() const { return m_currentUserId; }
    QString getCurrentLoginId() const { return m_currentLoginId; }
    
    void clearAuthentication();

signals:
    void authenticationChanged(bool authenticated);
    void loginCompleted(bool success, const QString& message);

private:
    NetworkClient* m_networkClient = nullptr;
    EncryptionService* m_encryptionService = nullptr;
    JsonParser* m_jsonParser = nullptr;
    
    // 认证状态
    QString m_currentToken;
    QString m_currentUserId;
    QString m_currentLoginId;
    
    // 内部方法
    AuthResult performPreCheck(const QString& username, const QString& encryptedPassword);
    AuthResult performLogin(const QString& username, const QString& encryptedPassword, const QString& loginId);
    void updateAuthenticationState(const AuthResult& result);
    QString buildPreCheckData(const QString& username, const QString& encryptedPassword) const;
    QString buildLoginData(const QString& username, const QString& encryptedPassword, const QString& loginId) const;
};

#endif // AUTHENTICATION_SERVICE_H
