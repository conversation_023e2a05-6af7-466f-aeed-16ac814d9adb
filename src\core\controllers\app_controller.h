#ifndef APP_CONTROLLER_H
#define APP_CONTROLLER_H

#include <QObject>
#include <QTimer>
#include "../models/account.h"
#include "../models/order.h"

class OrderService;
class NetworkManager;
class MainWindow;

/**
 * @brief 应用程序主控制器
 * 协调各个服务和UI组件之间的交互
 */
class AppController : public QObject {
    Q_OBJECT
    
public:
    explicit AppController(QObject* parent = nullptr);
    ~AppController();
    
    // 初始化
    bool initialize();
    void shutdown();
    
    // UI管理
    void setMainWindow(MainWindow* mainWindow);
    MainWindow* getMainWindow() const;
    void showMainWindow();
    void hideMainWindow();
    
    // 账号管理
    bool loginMainAccount(const QString& username, const QString& password);
    bool loginSubAccount(const AccountInfo& account);
    void logoutMainAccount();
    void logoutSubAccount(const QString& username);
    void logoutAllAccounts();
    
    // 批量登录
    void startBatchLogin(const QList<AccountInfo>& accounts);
    void stopBatchLogin();
    bool isBatchLoginRunning() const;
    
    // 订单管理
    void startOrderRefresh();
    void stopOrderRefresh();
    bool isOrderRefreshRunning() const;
    void refreshOrdersOnce();
    
    // 订单操作
    void acceptOrder(const QString& serialNo);
    void setOrderFilter(const OrderFilter& filter);
    OrderFilter getOrderFilter() const;
    
    // 应用状态
    enum AppState {
        INITIALIZING,
        READY,
        LOGGING_IN,
        REFRESHING_ORDERS,
        SHUTTING_DOWN,
        ERROR
    };
    AppState getState() const;
    QString getStateString() const;
    
    // 统计信息
    struct AppStats {
        int loggedInAccounts = 0;
        int totalOrders = 0;
        int availableOrders = 0;
        double totalOrderValue = 0.0;
        QDateTime lastRefreshTime;
        qint64 uptime = 0; // 运行时间(秒)
    };
    AppStats getStats() const;
    
    // 配置管理
    void saveConfiguration();
    void loadConfiguration();
    void resetConfiguration();

signals:
    // 状态信号
    void stateChanged(AppState newState, AppState oldState);
    void initialized();
    void shutdownCompleted();
    
    // 账号信号
    void mainAccountLoggedIn(const MainAccountInfo& account);
    void mainAccountLoggedOut();
    void subAccountLoggedIn(const AccountInfo& account);
    void subAccountLoggedOut(const QString& username);
    void batchLoginProgress(int current, int total);
    void batchLoginCompleted(int successful, int total);
    
    // 订单信号
    void ordersRefreshed(const QList<OrderInfo>& orders);
    void orderAccepted(const QString& serialNo, bool success);
    void newOrderFound(const OrderInfo& order);
    
    // 统计信号
    void statsUpdated(const AppStats& stats);
    
    // 错误信号
    void errorOccurred(const QString& error);
    void criticalError(const QString& error);

private slots:
    // 服务回调
    void onOrderServiceRefreshCompleted(bool success, const QString& message);
    void onOrderServiceOrdersUpdated(const QList<OrderInfo>& orders);
    void onOrderServiceErrorOccurred(const QString& error);
    
    // 网络回调
    void onNetworkManagerEngineHealthChanged(const QString& engineName, bool healthy);
    
    // 定时器回调
    void onStatsUpdateTimer();
    void onUptimeTimer();

private:
    // 内部方法
    void setState(AppState newState);
    void initializeServices();
    void initializeNetworkManager();
    void connectSignals();
    void updateStats();
    void handleCriticalError(const QString& error);
    
    // 核心组件
    MainWindow* m_mainWindow = nullptr;
    OrderService* m_orderService = nullptr;
    NetworkManager* m_networkManager = nullptr;
    
    // 状态管理
    AppState m_currentState = AppState::INITIALIZING;
    QDateTime m_startTime;
    
    // 账号状态
    MainAccountInfo m_mainAccount;
    QList<AccountInfo> m_subAccounts;
    bool m_batchLoginRunning = false;
    int m_batchLoginCurrent = 0;
    int m_batchLoginTotal = 0;
    
    // 统计定时器
    QTimer* m_statsUpdateTimer = nullptr;
    QTimer* m_uptimeTimer = nullptr;
    AppStats m_currentStats;
    
    // 错误处理
    int m_consecutiveErrors = 0;
    static const int MAX_CONSECUTIVE_ERRORS = 5;
};

#endif // APP_CONTROLLER_H
