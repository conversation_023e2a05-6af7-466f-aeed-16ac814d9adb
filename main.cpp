#include <QApplication>
#include <QStyleFactory>
#include <QDebug>
#include <QLoggingCategory>
#include <exception>
#include "src/ui/mainwindow.h"
#include "src/core/utils/logger.h"

// Qt消息处理器
void messageOutput(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    Q_UNUSED(context); // 明确表示不使用context参数
    QString txt;
    switch (type) {
    case QtDebugMsg:
        txt = QString("Debug: %1").arg(msg);
        break;
    case QtWarningMsg:
        txt = QString("Warning: %1").arg(msg);
        break;
    case QtCriticalMsg:
        txt = QString("Critical: %1").arg(msg);
        break;
    case QtFatalMsg:
        txt = QString("Fatal: %1").arg(msg);
        break;
    case QtInfoMsg:
        txt = QString("Info: %1").arg(msg);
        break;
    }

    // 输出到控制台
    fprintf(stderr, "%s\n", txt.toLocal8Bit().constData());
    fflush(stderr);
}

int main(int argc, char *argv[])
{
    try {
        // 安装消息处理器
        qInstallMessageHandler(messageOutput);

        NEW_LOG_INFO(NewLogCategory::SYSTEM, "应用程序启动");

        QApplication app(argc, argv);

        // 设置应用程序信息
        app.setApplicationName("订单管理器");
        app.setApplicationVersion("1.0.0");
        app.setOrganizationName("OrderManager");

        // 设置应用程序样式
        app.setStyle(QStyleFactory::create("Fusion"));

        NEW_LOG_INFO(NewLogCategory::SYSTEM, "创建主窗口");
        MainWindow window;
        window.show();

        NEW_LOG_INFO(NewLogCategory::SYSTEM, "进入事件循环");
        return app.exec();

    } catch (const std::exception& e) {
        NEW_LOG_ERROR(NewLogCategory::SYSTEM, QString("主程序捕获异常: %1").arg(e.what()));
        return -1;
    } catch (...) {
        NEW_LOG_ERROR(NewLogCategory::SYSTEM, "主程序捕获未知异常");
        return -1;
    }
}