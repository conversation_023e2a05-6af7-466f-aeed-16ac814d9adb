#ifndef JSON_HELPER_H
#define JSON_HELPER_H

#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QString>
#include <QStringList>
#include <QVariant>

/**
 * @brief JSON处理辅助工具类
 */
class JsonHelper {
public:
    // JSON解析
    static QJsonObject parseObject(const QString& jsonString, bool* ok = nullptr);
    static QJsonArray parseArray(const QString& jsonString, bool* ok = nullptr);
    
    // JSON生成
    static QString stringify(const QJsonObject& object, bool compact = false);
    static QString stringify(const QJsonArray& array, bool compact = false);
    
    // 安全的值提取
    static QString getString(const QJsonObject& obj, const QString& key, const QString& defaultValue = "");
    static int getInt(const QJsonObject& obj, const QString& key, int defaultValue = 0);
    static double getDouble(const QJsonObject& obj, const QString& key, double defaultValue = 0.0);
    static bool getBool(const QJsonObject& obj, const QString& key, bool defaultValue = false);
    static QJsonObject getObject(const QJsonObject& obj, const QString& key);
    static QJsonArray getArray(const QJsonObject& obj, const QString& key);
    
    // 嵌套路径访问 (例如: "user.profile.name")
    static QJsonValue getValueByPath(const QJsonObject& obj, const QString& path);
    static QString getStringByPath(const QJsonObject& obj, const QString& path, const QString& defaultValue = "");
    
    // 数组转换
    static QStringList toStringList(const QJsonArray& array);
    static QJsonArray fromStringList(const QStringList& list);
    
    // 对象合并
    static QJsonObject merge(const QJsonObject& base, const QJsonObject& overlay);
    
    // 验证
    static bool hasKey(const QJsonObject& obj, const QString& key);
    static bool hasPath(const QJsonObject& obj, const QString& path);
    static bool isValidJson(const QString& jsonString);
    
    // 格式化
    static QString prettyPrint(const QJsonObject& obj);
    static QString prettyPrint(const QJsonArray& array);
    
    // 过滤和转换
    static QJsonObject filterKeys(const QJsonObject& obj, const QStringList& allowedKeys);
    static QJsonObject removeKeys(const QJsonObject& obj, const QStringList& keysToRemove);
    
    // 比较
    static bool equals(const QJsonObject& obj1, const QJsonObject& obj2);
    static bool equals(const QJsonArray& arr1, const QJsonArray& arr2);

private:
    JsonHelper() = delete; // 工具类，不允许实例化
    
    static QJsonValue getValueByPathRecursive(const QJsonObject& obj, const QStringList& pathParts, int index);
};

/**
 * @brief JSON构建器 - 链式调用风格
 */
class JsonBuilder {
public:
    JsonBuilder();
    
    // 添加键值对
    JsonBuilder& add(const QString& key, const QString& value);
    JsonBuilder& add(const QString& key, int value);
    JsonBuilder& add(const QString& key, double value);
    JsonBuilder& add(const QString& key, bool value);
    JsonBuilder& add(const QString& key, const QJsonObject& value);
    JsonBuilder& add(const QString& key, const QJsonArray& value);
    
    // 条件添加
    JsonBuilder& addIf(bool condition, const QString& key, const QString& value);
    JsonBuilder& addIf(bool condition, const QString& key, int value);
    JsonBuilder& addIf(bool condition, const QString& key, double value);
    
    // 嵌套对象
    JsonBuilder& beginObject(const QString& key);
    JsonBuilder& endObject();
    
    // 数组
    JsonBuilder& beginArray(const QString& key);
    JsonBuilder& addToArray(const QString& value);
    JsonBuilder& addToArray(int value);
    JsonBuilder& addToArray(double value);
    JsonBuilder& addToArray(const QJsonObject& value);
    JsonBuilder& endArray();
    
    // 构建结果
    QJsonObject build() const;
    QString buildString(bool compact = false) const;
    
    // 重置
    JsonBuilder& reset();

private:
    QJsonObject m_root;
    QJsonObject* m_current;
    QJsonArray* m_currentArray;
    
    struct Context {
        QJsonObject* object;
        QJsonArray* array;
        QString key;
    };
    QList<Context> m_contextStack;
};

// 便捷宏
#define JSON_BUILDER() JsonBuilder()
#define JSON_GET_STRING(obj, key, def) JsonHelper::getString(obj, key, def)
#define JSON_GET_INT(obj, key, def) JsonHelper::getInt(obj, key, def)
#define JSON_GET_DOUBLE(obj, key, def) JsonHelper::getDouble(obj, key, def)
#define JSON_GET_BOOL(obj, key, def) JsonHelper::getBool(obj, key, def)

#endif // JSON_HELPER_H
