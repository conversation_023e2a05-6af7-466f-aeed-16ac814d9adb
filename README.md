# 订单管理器 (OrderManager)

基于Qt6 C++开发的订单管理系统，支持批量登录和批量刷新订单功能，使用HTTP代理。

## 功能特性

- ✅ 批量登录账号
- ✅ 批量刷新订单
- ✅ HTTP代理支持
- ✅ 多线程处理
- ✅ 实时日志显示
- ✅ 订单列表展示

## 编译要求

- Qt6 (Core, Widgets, Network)
- CMake 3.16+
- C++17 编译器

## 编译步骤

1. 确保已安装Qt6和CMake
2. 克隆或下载项目文件
3. 在项目根目录执行：

```bash
mkdir build
cd build
cmake ..
make
```

## 使用方法

1. 准备账号文件 `代理ip.txt`，格式如下：
```
用户名,密码,代理主机,代理端口
13064756431,159357ccc,***************,50001
19840171384,159357ccc,*************,50002
```

2. 运行程序：
```bash
./OrderManager
```

3. 在界面中：
   - 点击"加载账号"按钮加载账号文件
   - 选择游戏类型
   - 点击"开始刷新"开始批量刷新订单
   - 点击"停止"停止刷新

## 项目结构

```
OrderManager/
├── CMakeLists.txt          # CMake配置文件
├── main.cpp                # 主程序入口
├── mainwindow.h           # 主窗口头文件
├── mainwindow.cpp         # 主窗口实现
├── mainwindow.ui          # 主窗口UI文件
├── orderapi.h             # API接口头文件
├── orderapi.cpp           # API接口实现
├── proxyconfig.h          # 代理配置头文件
├── proxyconfig.cpp        # 代理配置实现
└── README.md              # 项目说明
```

## API接口

### 登录接口
- URL: `https://server.dailiantong.com.cn/API/AppService.ashx?Action=GoHome`
- 方法: POST
- 参数: LoginID, Pass, OS, verifystr, HD, Channels, UserID, TimeStamp, Ver, AppVer, AppOS, AppID, Sign

### 刷新订单接口
- URL: `https://server.dailiantong.com.cn/API/AppService.ashx?Action=LevelOrderList`
- 方法: POST
- 参数: IsPub, GameID, UserID, TimeStamp, Ver, AppVer, AppOS, AppID, Sign

## 加密算法

### 密码加密
```cpp
QString passwordEncrypt(const QString &password, const QString &loginId)
{
    QString pwdMD5 = md5Hash(password);
    QString combined = pwdMD5 + loginId;
    return md5Hash(combined);
}
```

### 签名加密
```cpp
QString signEncrypt(const QString &action, const QString &data, const QString &token)
{
    // 解析data参数，提取值部分
    QStringList pairs = data.split('&');
    QString unescapedData;
    
    for (const QString &pair : pairs) {
        int equalPos = pair.indexOf('=');
        if (equalPos != -1) {
            QString value = pair.mid(equalPos + 1);
            unescapedData += QUrl::fromPercentEncoding(value.toUtf8());
        }
    }
    
    QString signStr = "9c7b9399680658d308691f2acad58c0a" + action + unescapedData + token;
    return md5Hash(signStr);
}
```

## 注意事项

1. 确保网络连接正常
2. 代理服务器需要支持HTTP协议
3. 账号文件格式必须正确
4. 建议在测试环境中先验证功能

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。 