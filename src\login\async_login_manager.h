#ifndef ASYNC_LOGIN_MANAGER_H
#define ASYNC_LOGIN_MANAGER_H

#include <QObject>
#include <QTimer>
#include <QThreadPool>
#include <QMutex>
#include <QAtomicInt>
#include <functional>

class OrderAPI;

struct LoginTask {
    int accountIndex;
    QString username;
    QString password;
    QString proxyString;
    OrderAPI* api;
    std::function<void(int, bool, const QString&)> callback;
};

struct LoginResult {
    int accountIndex;
    bool success;
    QString message;
    QString token;
    QString userId;
    QString uid;
};

class AsyncLoginManager : public QObject
{
    Q_OBJECT

public:
    explicit AsyncLoginManager(QObject *parent = nullptr);
    ~AsyncLoginManager();

    // 开始异步批量登录
    void startBatchLogin(const QList<LoginTask>& tasks);
    
    // 停止所有登录任务
    void stopAllTasks();
    
    // 获取进度
    int getTotalTasks() const { return m_totalTasks; }
    int getCompletedTasks() const { return m_completedTasks; }
    int getSuccessfulTasks() const { return m_successfulTasks; }

signals:
    // 单个账号登录完成
    void accountLoginCompleted(const LoginResult& result);
    
    // 批量登录完成
    void batchLoginCompleted(int successful, int total);
    
    // 进度更新
    void progressUpdated(int completed, int total);

private slots:
    void onTaskCompleted(const LoginResult& result);

private:
    void executeLoginTask(const LoginTask& task);
    void processLoginResponse(int accountIndex, const QString& response, 
                            const std::function<void(int, bool, const QString&)>& callback);

private:
    QThreadPool* m_threadPool;
    QMutex m_resultMutex;
    QAtomicInt m_totalTasks;
    QAtomicInt m_completedTasks;
    QAtomicInt m_successfulTasks;
    bool m_stopped;
};

#endif // ASYNC_LOGIN_MANAGER_H
