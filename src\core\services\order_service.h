#ifndef ORDER_SERVICE_H
#define ORDER_SERVICE_H

#include <QObject>
#include <QTimer>
#include <QMutex>
#include "../models/order.h"
#include "../models/account.h"

class INetworkEngine;
class NetworkManager;

/**
 * @brief 订单服务配置
 */
struct OrderServiceConfig {
    int refreshInterval = 1800;     // 刷新间隔(ms)
    int maxRetryCount = 3;          // 最大重试次数
    int requestTimeout = 30000;     // 请求超时(ms)
    bool enableAutoRefresh = true;  // 启用自动刷新
    bool enableAutoAccept = false;  // 启用自动接单
    QString gameId = "110";         // 游戏ID
    int pageSize = 50;              // 页面大小
    
    bool isValid() const;
};

/**
 * @brief 订单服务
 * 负责订单的获取、刷新、接单等业务逻辑
 */
class OrderService : public QObject {
    Q_OBJECT
    
public:
    explicit OrderService(QObject* parent = nullptr);
    ~OrderService();
    
    // 服务配置
    void setConfig(const OrderServiceConfig& config);
    OrderServiceConfig getConfig() const;
    
    // 网络引擎
    void setNetworkManager(NetworkManager* networkManager);
    NetworkManager* getNetworkManager() const;
    
    // 账号管理
    void setMainAccount(const MainAccountInfo& account);
    void addSubAccount(const AccountInfo& account);
    void removeSubAccount(const QString& username);
    void clearSubAccounts();
    QList<AccountInfo> getLoggedInAccounts() const;
    
    // 订单刷新
    void startAutoRefresh();
    void stopAutoRefresh();
    bool isAutoRefreshing() const;
    void refreshOrders();
    void refreshOrdersForAccount(const QString& accountId);
    
    // 订单管理
    QList<OrderInfo> getOrders() const;
    QList<OrderInfo> getFilteredOrders(const OrderFilter& filter) const;
    OrderStats getOrderStats() const;
    
    // 订单操作
    void acceptOrder(const QString& serialNo, const QString& accountId = "");
    void cancelOrder(const QString& serialNo);
    
    // 过滤器管理
    void setOrderFilter(const OrderFilter& filter);
    OrderFilter getOrderFilter() const;
    
    // 状态查询
    bool isRefreshing() const;
    QString getCurrentRefreshingAccount() const;
    int getRefreshProgress() const; // 0-100
    
    // 性能统计
    struct PerformanceStats {
        int totalRefreshes = 0;
        int successfulRefreshes = 0;
        int failedRefreshes = 0;
        qint64 totalRefreshTime = 0;
        qint64 averageRefreshTime = 0;
        QDateTime startTime;
        QDateTime lastRefreshTime;
        
        double getSuccessRate() const {
            return totalRefreshes > 0 ? (double)successfulRefreshes / totalRefreshes * 100.0 : 0.0;
        }
    };
    PerformanceStats getPerformanceStats() const;
    void resetPerformanceStats();

signals:
    // 订单信号
    void ordersUpdated(const QList<OrderInfo>& orders);
    void orderAdded(const OrderInfo& order);
    void orderUpdated(const OrderInfo& order);
    void orderRemoved(const QString& serialNo);
    
    // 刷新信号
    void refreshStarted();
    void refreshCompleted(bool success, const QString& message);
    void refreshProgress(int percentage);
    void accountRefreshStarted(const QString& accountId);
    void accountRefreshCompleted(const QString& accountId, bool success);
    
    // 接单信号
    void orderAcceptStarted(const QString& serialNo);
    void orderAcceptCompleted(const QString& serialNo, bool success, const QString& message);
    
    // 错误信号
    void errorOccurred(const QString& error);
    void networkError(const QString& error);
    
    // 统计信号
    void statsUpdated(const OrderStats& stats);
    void performanceStatsUpdated(const PerformanceStats& stats);

private slots:
    void onRefreshTimer();
    void onNetworkRequestCompleted(const QString& engineName, const class NetworkResult& result);
    void onNetworkRequestFailed(const QString& engineName, const QString& error);

private:
    // 内部方法
    void refreshNextAccount();
    void processOrderResponse(const QString& response, const QString& accountId);
    void updateOrderStats();
    void updatePerformanceStats(bool success, qint64 responseTime);
    
    // 配置和状态
    OrderServiceConfig m_config;
    NetworkManager* m_networkManager = nullptr;
    
    // 账号管理
    MainAccountInfo m_mainAccount;
    QList<AccountInfo> m_subAccounts;
    
    // 订单管理
    OrderManager m_orderManager;
    OrderFilter m_orderFilter;
    
    // 刷新状态
    QTimer* m_refreshTimer = nullptr;
    bool m_isRefreshing = false;
    int m_currentAccountIndex = -1; // -1表示主账号
    QString m_currentRefreshingAccount;
    
    // 性能统计
    PerformanceStats m_performanceStats;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // 请求管理
    struct PendingRequest {
        QString requestId;
        QString accountId;
        QDateTime startTime;
        int retryCount = 0;
    };
    QHash<QString, PendingRequest> m_pendingRequests;
};

#endif // ORDER_SERVICE_H
